//! Global Compute Resource Manager
//!
//! Manages computation resources across multiple nodes to prevent system overload.
//! Only allows a limited number of nodes to compute proofs simultaneously.

use std::sync::Arc;
use tokio::sync::Semaphore;
use std::time::Duration;

/// Global compute resource manager that limits concurrent proof computations
#[derive(Clone)]
pub struct ComputeManager {
    /// Semaphore to limit concurrent computations
    compute_semaphore: Arc<Semaphore>,
    /// Maximum concurrent computations allowed
    max_concurrent: usize,
}

impl ComputeManager {
    /// Create a new compute manager with the specified maximum concurrent computations
    pub fn new(max_concurrent: usize) -> Self {
        println!("🧮 Initializing Compute Manager: {} concurrent computations allowed", max_concurrent);
        Self {
            compute_semaphore: Arc::new(Semaphore::new(max_concurrent)),
            max_concurrent,
        }
    }

    /// Create a compute manager with automatic resource detection
    pub fn new_auto() -> Self {
        let max_concurrent = Self::calculate_max_concurrent();
        Self::new(max_concurrent)
    }

    /// Calculate maximum concurrent computations based on system resources
    fn calculate_max_concurrent() -> usize {
        let memory_gb = crate::system::total_memory_gb();
        let cores = crate::system::num_cores();

        // Optimized calculation based on real-world testing:
        // - Each ZK proof computation needs ~1.5-2GB memory (based on Nexus zkVM benchmarks)
        // - Use 75% of available memory for better utilization
        // - Use 75% of CPU cores for better performance
        // - Minimum 1, maximum 32 concurrent computations for high-spec servers

        let memory_limit = ((memory_gb * 0.75) / 2.0) as usize;
        let core_limit = (cores as f64 * 0.75) as usize;
        let hard_limit = 32; // Increased based on real memory usage data

        let max_concurrent = memory_limit.min(core_limit).min(hard_limit).max(1);
        
        println!("💾 System memory: {:.1}GB", memory_gb);
        println!("🔧 CPU cores: {}", cores);
        println!("🧮 Calculated max concurrent computations: {} (memory: {}, cores: {}, hard limit: {})", 
                 max_concurrent, memory_limit, core_limit, hard_limit);
        
        max_concurrent
    }

    /// Acquire a computation permit (blocks until available)
    pub async fn acquire_compute_permit(&self) -> ComputePermit {
        let permit = self.compute_semaphore.clone().acquire_owned().await.expect("Semaphore closed");
        ComputePermit { _permit: permit }
    }

    /// Try to acquire a computation permit without blocking
    pub fn try_acquire_compute_permit(&self) -> Option<ComputePermit> {
        self.compute_semaphore.clone().try_acquire_owned().ok().map(|permit| ComputePermit { _permit: permit })
    }

    /// Acquire a computation permit with timeout
    pub async fn acquire_compute_permit_timeout(&self, timeout: Duration) -> Option<ComputePermit> {
        tokio::time::timeout(timeout, self.acquire_compute_permit()).await.ok()
    }

    /// Get the number of available computation permits
    pub fn available_permits(&self) -> usize {
        self.compute_semaphore.available_permits()
    }

    /// Get the maximum number of concurrent computations
    pub fn max_concurrent(&self) -> usize {
        self.max_concurrent
    }

    /// Get the number of currently active computations
    pub fn active_computations(&self) -> usize {
        self.max_concurrent - self.available_permits()
    }
}

/// A permit that allows one computation to proceed
/// Automatically releases the permit when dropped
pub struct ComputePermit {
    _permit: tokio::sync::OwnedSemaphorePermit,
}

impl ComputePermit {
    /// Get a reference to this permit (for logging purposes)
    pub fn id(&self) -> String {
        format!("permit-{:p}", &self._permit)
    }
}

// Global compute manager instance
static COMPUTE_MANAGER: std::sync::OnceLock<ComputeManager> = std::sync::OnceLock::new();

/// Initialize the global compute manager
pub fn init_global_compute_manager(max_concurrent: Option<usize>) {
    let manager = match max_concurrent {
        Some(max) => ComputeManager::new(max),
        None => ComputeManager::new_auto(),
    };
    
    if COMPUTE_MANAGER.set(manager).is_err() {
        eprintln!("⚠️  Warning: Global compute manager already initialized");
    }
}

/// Get the global compute manager instance
pub fn get_global_compute_manager() -> &'static ComputeManager {
    COMPUTE_MANAGER.get().expect("Global compute manager not initialized. Call init_global_compute_manager() first.")
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_compute_manager_basic() {
        let manager = ComputeManager::new(2);
        
        // Should be able to acquire permits up to the limit
        let permit1 = manager.acquire_compute_permit().await;
        let permit2 = manager.acquire_compute_permit().await;
        
        assert_eq!(manager.available_permits(), 0);
        assert_eq!(manager.active_computations(), 2);
        
        // Should not be able to acquire more permits
        assert!(manager.try_acquire_compute_permit().is_none());
        
        // Drop one permit
        drop(permit1);
        assert_eq!(manager.available_permits(), 1);
        assert_eq!(manager.active_computations(), 1);
        
        // Should be able to acquire again
        let _permit3 = manager.acquire_compute_permit().await;
        assert_eq!(manager.available_permits(), 0);
    }

    #[tokio::test]
    async fn test_compute_manager_timeout() {
        let manager = ComputeManager::new(1);
        
        // Acquire the only permit
        let _permit1 = manager.acquire_compute_permit().await;
        
        // Should timeout when trying to acquire another
        let result = manager.acquire_compute_permit_timeout(Duration::from_millis(100)).await;
        assert!(result.is_none());
    }
}
