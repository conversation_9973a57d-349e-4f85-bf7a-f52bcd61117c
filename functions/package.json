{"name": "nexus-cli-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for Nexus CLI", "engines": {"node": "20"}, "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "axios": "^1.6.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "private": true}