//! Task Expiration Detector
//!
//! Detects when server-side tasks have expired by monitoring consecutive 404 errors
//! and triggers soft restart of the node to clear all state and start fresh.

use crate::consts::prover::{CONSECUTIVE_404_THRESHOLD, CONSECUTIVE_404_WINDOW};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{broadcast, Mutex};

/// Global task expiration detector for tracking 404 errors across all workers
#[derive(<PERSON><PERSON>, Debug)]
pub struct TaskExpirationDetector {
    inner: Arc<Mutex<TaskExpirationState>>,
    restart_sender: Arc<Mutex<Option<broadcast::Sender<()>>>>,
}

#[derive(Debug)]
struct TaskExpirationState {
    consecutive_404_count: u32,
    last_404_time: Option<Instant>,
}

impl TaskExpirationDetector {
    pub fn new() -> Self {
        Self {
            inner: Arc::new(Mutex::new(TaskExpirationState {
                consecutive_404_count: 0,
                last_404_time: None,
            })),
            restart_sender: Arc::new(Mutex::new(None)),
        }
    }

    /// Set the restart signal sender (called during node initialization)
    pub async fn set_restart_sender(&self, sender: broadcast::Sender<()>) {
        let mut restart_sender = self.restart_sender.lock().await;
        *restart_sender = Some(sender);
    }

    /// Record a 404 error and trigger soft restart if threshold is reached
    pub async fn record_404_error(&self) -> bool {
        let mut state = self.inner.lock().await;
        let now = Instant::now();

        // Check if this 404 is within the time window of the last one
        if let Some(last_404) = state.last_404_time {
            if now.duration_since(last_404) <= Duration::from_millis(CONSECUTIVE_404_WINDOW) {
                state.consecutive_404_count += 1;
            } else {
                // Reset count if too much time has passed
                state.consecutive_404_count = 1;
            }
        } else {
            state.consecutive_404_count = 1;
        }

        state.last_404_time = Some(now);

        // Trigger soft restart if threshold is reached
        if state.consecutive_404_count >= CONSECUTIVE_404_THRESHOLD {
            // Send restart signal to all workers
            if let Some(sender) = self.restart_sender.lock().await.as_ref() {
                let _ = sender.send(());
            }

            // Reset the counter after triggering restart
            state.consecutive_404_count = 0;
            state.last_404_time = None;

            return true;
        }

        false
    }

    /// Reset 404 error tracking (called after successful operations)
    pub async fn reset_tracking(&self) {
        let mut state = self.inner.lock().await;
        state.consecutive_404_count = 0;
        state.last_404_time = None;
    }

    /// Get current 404 error count for debugging
    pub async fn get_404_count(&self) -> u32 {
        let state = self.inner.lock().await;
        state.consecutive_404_count
    }
}

/// Per-node instance storage using node ID as key
static NODE_DETECTORS: tokio::sync::OnceCell<std::sync::Arc<tokio::sync::Mutex<std::collections::HashMap<u64, TaskExpirationDetector>>>> = tokio::sync::OnceCell::const_new();

/// Initialize the node detectors storage
pub fn init_global_detector() {
    let _ = NODE_DETECTORS.set(std::sync::Arc::new(tokio::sync::Mutex::new(std::collections::HashMap::new())));
}

/// Get or create a task expiration detector for a specific node
pub async fn get_node_detector(node_id: u64) -> TaskExpirationDetector {
    let detectors = NODE_DETECTORS.get().expect("Task expiration detector not initialized");
    let mut detectors_map = detectors.lock().await;

    detectors_map.entry(node_id)
        .or_insert_with(|| TaskExpirationDetector::new())
        .clone()
}

/// Record a 404 error for a specific node and return whether cleanup should be triggered
pub async fn record_404_error(node_id: u64) -> bool {
    get_node_detector(node_id).await.record_404_error().await
}

/// Reset 404 error tracking for a specific node
pub async fn reset_404_tracking(node_id: u64) {
    get_node_detector(node_id).await.reset_tracking().await;
}

/// Get current 404 error count for debugging for a specific node
pub async fn get_404_count(node_id: u64) -> u32 {
    get_node_detector(node_id).await.get_404_count().await
}

/// Set the restart signal sender for soft restart functionality for a specific node
pub async fn set_restart_sender(node_id: u64, sender: broadcast::Sender<()>) {
    get_node_detector(node_id).await.set_restart_sender(sender).await;
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::broadcast;

    #[tokio::test]
    async fn test_404_error_detection() {
        let detector = TaskExpirationDetector::new();
        let (sender, _receiver) = broadcast::channel(1);
        detector.set_restart_sender(sender).await;

        // First 404 error should not trigger restart
        assert!(!detector.record_404_error().await);
        assert_eq!(detector.get_404_count().await, 1);

        // Second 404 error should not trigger restart
        assert!(!detector.record_404_error().await);
        assert_eq!(detector.get_404_count().await, 2);

        // Third 404 error should trigger restart (threshold is 3)
        assert!(detector.record_404_error().await);
        assert_eq!(detector.get_404_count().await, 0); // Reset after restart

        // Test reset functionality
        detector.reset_tracking().await;
        assert_eq!(detector.get_404_count().await, 0);
    }

    #[tokio::test]
    async fn test_404_error_time_window() {
        let detector = TaskExpirationDetector::new();
        let (sender, _receiver) = broadcast::channel(1);
        detector.set_restart_sender(sender).await;

        // Record first error
        assert!(!detector.record_404_error().await);
        assert_eq!(detector.get_404_count().await, 1);

        // Simulate time passing beyond the window
        {
            let mut state = detector.inner.lock().await;
            state.last_404_time = Some(
                std::time::Instant::now() - std::time::Duration::from_millis(CONSECUTIVE_404_WINDOW + 1000)
            );
        }

        // Next error should reset the count
        assert!(!detector.record_404_error().await);
        assert_eq!(detector.get_404_count().await, 1);
    }
}
