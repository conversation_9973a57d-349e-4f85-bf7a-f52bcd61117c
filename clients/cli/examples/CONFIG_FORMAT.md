# Nexus CLI 多节点配置格式说明

## 支持的配置格式

Nexus CLI 现在支持三种节点配置格式，可以在同一个配置文件中混合使用：

### 1. 纯节点ID（不使用代理）
```
12345
67890
11111
```

**说明**：
- 直接写节点ID，一行一个
- 不使用任何代理，直接连接到 Nexus 网络
- 适合网络环境良好的情况

### 2. HTTP代理格式
```
http://username:<EMAIL>:8080,12345
http://user:<EMAIL>:3128,67890
```

**说明**：
- 格式：`http://用户名:密码@代理地址:端口,节点ID`
- 支持 HTTP 和 HTTPS 代理
- 用户名和密码可选
- 适合使用 HTTP 代理的环境

### 3. SOCKS5代理格式
```
socks5://username:<EMAIL>:1080,12345
socks5h://user:<EMAIL>:1080,67890
```

**说明**：
- 格式：`socks5://用户名:密码@代理地址:端口,节点ID`
- 支持 `socks5://` 和 `socks5h://`
- 用户名和密码可选
- 适合使用 SOCKS5 代理的环境

## 配置文件示例

```csv
# 这是注释行，以 # 开头的行会被忽略

# 不使用代理的节点
12345
67890
11111

# 使用HTTP代理的节点
http://user1:<EMAIL>:8080,22222
http://user2:<EMAIL>:3128,33333

# 使用SOCKS5代理的节点
socks5://user3:<EMAIL>:1080,44444
socks5://user4:<EMAIL>:1080,55555

# 混合配置
99999
http://proxyuser:<EMAIL>:8080,88888
socks5://socksuser:<EMAIL>:1080,77777
66666
```

## 使用方法

### 基本用法
```bash
./nexus-cli start --config nodes.csv
```

### 指定行范围
```bash
# 只运行第1-10行的节点
./nexus-cli start --config nodes.csv --start-line 1 --end-line 10

# 从第5行开始运行所有节点
./nexus-cli start --config nodes.csv --start-line 5

# 只运行前20行的节点
./nexus-cli start --config nodes.csv --end-line 20
```

### 控制并发数量
```bash
# 最多同时运行8个节点
./nexus-cli start --config nodes.csv --max-concurrent 8
```

### 完整参数示例
```bash
./nexus-cli start \
  --config nodes.csv \
  --start-line 1 \
  --end-line 50 \
  --max-concurrent 16 \
  --max-threads 2
```

## 注意事项

1. **节点ID**：必须是有效的数字
2. **代理认证**：用户名和密码中如果包含特殊字符，需要进行URL编码
3. **注释行**：以 `#` 开头的行会被忽略
4. **空行**：空行会被自动跳过
5. **混合使用**：可以在同一个文件中混合使用三种格式
6. **资源控制**：建议根据系统性能合理设置 `--max-concurrent` 参数

## 优势

- **灵活性**：支持有代理和无代理节点混合运行
- **简化配置**：无代理节点只需要写节点ID
- **向后兼容**：完全兼容之前的代理配置格式
- **资源优化**：无代理节点减少网络开销，提高性能
