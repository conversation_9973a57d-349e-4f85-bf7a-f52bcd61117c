[package]
name = "nexus-network"
version = "0.10.3"
edition = "2024"
rust-version = "1.85"
build = "build.rs"

[features]
build_proto = []

[[bin]]
name = "nexus-network"
path = "src/main.rs"

[profile.dev]
opt-level = 1

[profile.release]
opt-level = 3          # Maximum optimization level
lto = true             # Link Time Optimization for better performance
codegen-units = 1      # Single codegen unit for maximum optimization
panic = 'abort'        # Abort on panic (smaller binaries, better performance)
strip = true           # Strip symbols for smaller binaries
debug = true           # Keep debug symbols for easier debugging

[profile.ci-build]
inherits = "dev"
opt-level = 0
debug = 0
strip = "none"
lto = false
codegen-units = 256
incremental = true

[dependencies]
async-trait = "0.1.88"
cfg-if = "1.0"
chrono = "0.4.38"
clap = { version = "4.5", features = ["derive"] }
crossterm = "0.29.0"
ed25519-dalek = { version = "2", features = ["rand_core"] }

home = "0.5.9"
iana-time-zone = "0.1.60"
libc = "0.2"
log = "0.4.26"
nexus-sdk = { git = "https://github.com/nexus-xyz/nexus-zkvm", tag = "0.3.4" }
postcard = "1.0.10"
prost = "0.13"
prost-types = "0.13.5"
rand = "0.8"
rand_core = "0.6"
ratatui = "0.29.0"
reqwest = { version = "0.12", default-features = false, features = ["json", "rustls-tls", "socks"] }
serde = { version = "1.0.217", features = ["derive"] }
serde_json = { version = "1.0.138" }
sha3 = "0.10.8"
strum = "0.26.3"
sysinfo = "0.33.1"
thiserror = "2.0.12"
tokio = { version = "1.38", features = ["full"] }
url = "2.5"

urlencoding = "2.1.3"
uuid = "1.16.0"
semver = "1.0"

[dev-dependencies]
assert_cmd = "2"
async-trait = "0.1.88"
mockall = "0.12"
predicates = "3"
tempfile = "3.20.0"

[build-dependencies]
prost-build = "0.13"
