// Copyright (c) 2024 Nexus. All rights reserved.

mod analytics;
mod compute_manager;
mod config;
mod consts;
mod environment;
mod error_classifier;
mod events;
mod keys;
mod logging;
mod memory_monitor;
#[path = "proto/nexus.orchestrator.rs"]
mod nexus_orchestrator;
mod orchestrator;
mod pretty;
mod prover;
mod prover_runtime;
mod register;

pub mod system;
mod task;
mod task_cache;
mod task_expiration_detector;
mod ui;
mod version_checker;
mod version_requirements;
mod workers;

use crate::config::{Config, get_config_path};
use crate::environment::Environment;
use crate::orchestrator::{Orchestrator, OrchestratorClient};
use crate::pretty::print_cmd_info;
use crate::prover_runtime::{start_anonymous_workers, start_authenticated_workers};
use crate::register::{register_node, register_user};
use crate::version_requirements::{VersionRequirements, VersionRequirementsError};
use clap::{ArgAction, Parser, Subcommand};
use crossterm::{
    event::{DisableMouseCapture, EnableMouseCapture},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ed25519_dalek::SigningKey;
use ratatui::{Terminal, backend::CrosstermBackend};
use std::{error::Error, io};
use tokio::sync::broadcast;

#[derive(Parser)]
#[command(author, version, about, long_about = None)]
/// Command-line arguments
struct Args {
    /// Command to execute
    #[command(subcommand)]
    command: Command,
}

#[derive(Subcommand)]
enum Command {
    /// Start the prover
    Start {
        /// Node ID (for single node mode)
        #[arg(long, value_name = "NODE_ID")]
        node_id: Option<u64>,

        /// Run without the terminal UI
        #[arg(long = "headless", action = ArgAction::SetTrue)]
        headless: bool,

        /// Maximum number of threads to use for proving.
        #[arg(long = "max-threads", value_name = "MAX_THREADS")]
        max_threads: Option<u32>,

        /// Custom orchestrator URL (overrides environment setting)
        #[arg(long = "orchestrator-url", value_name = "URL")]
        orchestrator_url: Option<String>,

        /// Multi-node configuration file path (supports HTTP and SOCKS5 URL formats)
        #[arg(long = "config", value_name = "CONFIG_FILE")]
        config_file: Option<String>,

        /// Start line number for reading config file (1-based, inclusive)
        #[arg(long = "start-line", value_name = "LINE")]
        start_line: Option<usize>,

        /// End line number for reading config file (1-based, inclusive)
        #[arg(long = "end-line", value_name = "LINE")]
        end_line: Option<usize>,

        /// Batch size for multi-node processing (overrides auto-calculation)
        #[arg(long = "batch-size", value_name = "SIZE")]
        batch_size: Option<usize>,

        /// Delay between batches in milliseconds
        #[arg(long = "batch-delay", value_name = "MS")]
        batch_delay: Option<u64>,

        /// Maximum concurrent proof computations (overrides auto-detection)
        #[arg(long = "max-concurrent", value_name = "COUNT")]
        max_concurrent: Option<usize>,

        /// Enable detailed memory usage monitoring and logging
        #[arg(long = "memory-debug", action = ArgAction::SetTrue)]
        memory_debug: bool,

        /// Disable background colors in the dashboard
        #[arg(long = "no-background-color", action = ArgAction::SetTrue)]
        no_background_color: bool,

        /// Maximum number of tasks to process before exiting (default: unlimited)
        #[arg(long = "max-tasks", value_name = "MAX_TASKS")]
        max_tasks: Option<u32>,
    },
    /// Register a new user
    RegisterUser {
        /// User's public Ethereum wallet address. 42-character hex string starting with '0x'
        #[arg(long, value_name = "WALLET_ADDRESS")]
        wallet_address: String,
    },
    /// Register a new node to an existing user, or link an existing node to a user.
    RegisterNode {
        /// ID of the node to register. If not provided, a new node will be created.
        #[arg(long, value_name = "NODE_ID")]
        node_id: Option<u64>,
    },
    /// Clear the node configuration and logout.
    Logout,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Set up panic hook to prevent core dumps
    std::panic::set_hook(Box::new(|panic_info| {
        eprintln!("Panic occurred: {}", panic_info);
        std::process::exit(1);
    }));

    let nexus_environment_str = std::env::var("NEXUS_ENVIRONMENT").unwrap_or_default();
    let environment = nexus_environment_str
        .parse::<Environment>()
        .unwrap_or(Environment::default());

    let config_path = get_config_path()?;

    let args = Args::parse();
    match args.command {
        Command::Start {
            node_id,
            headless,
            max_threads,
            orchestrator_url,
            config_file,
            start_line,
            end_line,
            batch_size,
            batch_delay,
            max_concurrent,
            memory_debug,
            no_background_color,
            max_tasks,
        } => {
            // If a custom orchestrator URL is provided, create a custom environment
            let final_environment = if let Some(url) = orchestrator_url {
                Environment::Custom {
                    orchestrator_url: url,
                }
            } else {
                environment
            };
            start(
                node_id,
                final_environment,
                config_path,
                headless,
                max_threads,
                config_file,
                start_line,
                end_line,
                batch_size,
                batch_delay,
                max_concurrent,
                memory_debug,
                no_background_color,
                max_tasks,
            )
            .await
        }
        Command::Logout => {
            print_cmd_info!("Logging out", "Clearing node configuration file...");
            Config::clear_node_config(&config_path).map_err(Into::into)
        }
        Command::RegisterUser { wallet_address } => {
            print_cmd_info!("Registering user", "Wallet address: {}", wallet_address);
            let orchestrator = Box::new(OrchestratorClient::new(environment));
            register_user(&wallet_address, &config_path, orchestrator).await
        }
        Command::RegisterNode { node_id } => {
            let orchestrator = Box::new(OrchestratorClient::new(environment));
            register_node(node_id, &config_path, orchestrator).await
        }
    }
}

/// Starts the Nexus CLI application.
///
/// # Arguments
/// * `node_id` - This client's unique identifier, if available.
/// * `env` - The environment to connect to.
/// * `config_path` - Path to the configuration file.
/// * `headless` - If true, runs without the terminal UI.
/// * `max_threads` - Optional maximum number of threads to use for proving.
/// * `config_file` - Optional path to multi-node configuration file.
/// * `batch_size` - Optional batch size for multi-node processing.
/// * `batch_delay` - Optional delay between batches in milliseconds.
async fn start(
    node_id: Option<u64>,
    env: Environment,
    config_path: std::path::PathBuf,
    headless: bool,
    max_threads: Option<u32>,
    config_file: Option<String>,
    start_line: Option<usize>,
    end_line: Option<usize>,
    batch_size: Option<usize>,
    batch_delay: Option<u64>,
    max_concurrent: Option<usize>,
    memory_debug: bool,
    no_background_color: bool,
    max_tasks: Option<u32>,
) -> Result<(), Box<dyn Error>> {
    // Check for multi-node configuration first
    if let Some(config_file_path) = config_file {
        return start_multi_node(
            env,
            config_file_path,
            start_line,
            end_line,
            headless,
            max_threads,
            batch_size,
            batch_delay,
            max_concurrent,
            memory_debug,
        ).await;
    }

    // Check version requirements before starting any workers
    match VersionRequirements::fetch().await {
        Ok(requirements) => {
            let current_version = env!("CARGO_PKG_VERSION");
            match requirements.check_version_constraints(current_version, None, None) {
                Ok(Some(violation)) => match violation.constraint_type {
                    crate::version_requirements::ConstraintType::Blocking => {
                        eprintln!("❌ Version requirement not met: {}", violation.message);
                        std::process::exit(1);
                    }
                    crate::version_requirements::ConstraintType::Warning => {
                        eprintln!("⚠️  {}", violation.message);
                    }
                    crate::version_requirements::ConstraintType::Notice => {
                        eprintln!("ℹ️  {}", violation.message);
                    }
                },
                Ok(None) => {
                    // No violations found, continue
                }
                Err(e) => {
                    eprintln!("❌ Failed to parse version requirements: {}", e);
                    eprintln!(
                        "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues/new"
                    );
                    std::process::exit(1);
                }
            }
        }
        Err(VersionRequirementsError::Fetch(e)) => {
            eprintln!("❌ Failed to fetch version requirements: {}", e);
            eprintln!(
                "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues/new"
            );
            std::process::exit(1);
        }
        Err(e) => {
            eprintln!("❌ Failed to check version requirements: {}", e);
            eprintln!(
                "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues/new"
            );
            std::process::exit(1);
        }
    }

    let mut node_id = node_id;

    // If no node ID is provided, try to load it from the config file.
    if node_id.is_none() && config_path.exists() {
        let config = Config::load_from_file(&config_path)?;

        // Check if user is registered but node_id is missing or invalid
        if !config.user_id.is_empty() {
            if config.node_id.is_empty() {
                print_cmd_info!(
                    "✅ User registered, but no node found.",
                    "Please register a node to continue: nexus-cli register-node"
                );
                return Err(
                    "Node registration required. Please run 'nexus-cli register-node' first."
                        .into(),
                );
            }

            match config.node_id.parse::<u64>() {
                Ok(id) => {
                    node_id = Some(id);
                    print_cmd_info!("✅ Found Node ID from config file", "Node ID: {}", id);
                }
                Err(_) => {
                    print_cmd_info!(
                        "❌ Invalid node ID in config file.",
                        "Please register a new node: nexus-cli register-node"
                    );
                    return Err("Invalid node ID in config. Please run 'nexus-cli register-node' to fix this.".into());
                }
            }
        } else {
            print_cmd_info!(
                "❌ No user registration found.",
                "Please register your wallet address first: nexus-cli register-user --wallet-address <your-wallet-address>"
            );
            return Err("User registration required. Please run 'nexus-cli register-user --wallet-address <your-wallet-address>' first.".into());
        }
    } else if node_id.is_none() {
        // No config file exists at all
        print_cmd_info!(
            "Welcome to Nexus CLI!",
            "Please register your wallet address to get started: nexus-cli register-user --wallet-address <your-wallet-address>"
        );
    }

    // Create a signing key for the prover.
    let mut csprng = rand_core::OsRng;
    let signing_key: SigningKey = SigningKey::generate(&mut csprng);
    let orchestrator_client = OrchestratorClient::new(env.clone());
    // Clamp the number of workers to [1,8]. Keep this low for now to avoid rate limiting.
    let num_workers: usize = max_threads.unwrap_or(1).clamp(1, 8) as usize;
    let (shutdown_sender, _) = broadcast::channel(1); // Only one shutdown signal needed

    // Get client_id for analytics - use wallet address from API if available, otherwise "anonymous"
    let client_id = if let Some(node_id) = node_id {
        match orchestrator_client.get_node(&node_id.to_string()).await {
            Ok(wallet_address) => {
                // Use wallet address as client_id for analytics
                wallet_address
            }
            Err(_) => {
                // If API call fails, use "anonymous" regardless of config
                "anonymous".to_string()
            }
        }
    } else {
        // No node_id available, use "anonymous"
        "anonymous".to_string()
    };

    let (mut event_receiver, mut join_handles) = match node_id {
        Some(node_id) => {
            start_authenticated_workers(
                node_id,
                signing_key.clone(),
                orchestrator_client.clone(),
                num_workers,
                shutdown_sender.subscribe(),
                env.clone(),
                client_id,
                max_tasks,
            )
            .await
        }
        None => {
            start_anonymous_workers(num_workers, shutdown_sender.subscribe(), env, client_id).await
        }
    };

    if !headless {
        // Terminal setup
        enable_raw_mode()?;
        let mut stdout = io::stdout();
        execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;

        // Initialize the terminal with Crossterm backend.
        let backend = CrosstermBackend::new(stdout);
        let mut terminal = Terminal::new(backend)?;

        // Create the application and run it.
        let app = ui::App::new(
            node_id,
            orchestrator_client.environment().clone(),
            event_receiver,
            shutdown_sender,
            no_background_color,
            num_workers,
        );
        let res = ui::run(&mut terminal, app).await;

        // Clean up the terminal after running the application.
        disable_raw_mode()?;
        execute!(
            terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        )?;
        terminal.show_cursor()?;

        res?;
    } else {
        // Headless mode: log events to console.

        // Trigger shutdown on Ctrl+C and other signals
        let shutdown_sender_clone = shutdown_sender.clone();
        tokio::spawn(async move {
            if tokio::signal::ctrl_c().await.is_ok() {
                let _ = shutdown_sender_clone.send(());
            }
        });

        // Also handle SIGTERM gracefully (Unix only)
        #[cfg(unix)]
        {
            let shutdown_sender_clone2 = shutdown_sender.clone();
            tokio::spawn(async move {
                if tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate()).is_ok() {
                    if let Ok(mut signal) =
                        tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
                    {
                        if signal.recv().await.is_some() {
                            let _ = shutdown_sender_clone2.send(());
                        }
                    }
                }
            });
        }

        let mut shutdown_receiver = shutdown_sender.subscribe();
        loop {
            tokio::select! {
                Some(event) = event_receiver.recv() => {
                    println!("{}", event);
                }
                _ = shutdown_receiver.recv() => {
                    break;
                }
            }
        }
    }
    println!("\nExiting...");
    for handle in join_handles.drain(..) {
        let _ = handle.await;
    }
    println!("Nexus CLI application exited successfully.");
    Ok(())
}



/// Format event messages with action icons for better readability
fn format_event_with_action(event: &crate::events::Event) -> String {
    let event_str = event.to_string();

    // Add icons based on worker type and message content
    let icon = match event.worker {
        crate::events::Worker::VersionChecker => "🔍",
        crate::events::Worker::TaskFetcher => {
            if event.msg.contains("Fetching tasks") {
                "📥"
            } else if event.msg.contains("Queue status") {
                "📊"
            } else {
                "📋"
            }
        },
        crate::events::Worker::Prover(_) => {
            if event.msg.contains("Proof completed successfully") {
                "✅"
            } else {
                "⚙️"
            }
        },
        crate::events::Worker::ProofSubmitter => {
            if event.msg.contains("Proof submitted") && event.msg.contains("Points for this node") {
                "🎯"
            } else if event.msg.contains("Failed to submit proof") {
                "❌"
            } else {
                "📤"
            }
        },
    };

    // Add additional context based on event type
    let type_icon = match event.event_type {
        crate::events::EventType::Success => "",  // Already handled by worker-specific icons
        crate::events::EventType::Error => "🚨",
        crate::events::EventType::Refresh => "🔄",
        crate::events::EventType::Shutdown => "🛑",
        crate::events::EventType::Waiting => "⏳",
    };

    if !type_icon.is_empty() && type_icon != icon {
        format!("{} {} {}", type_icon, icon, event_str)
    } else {
        format!("{} {}", icon, event_str)
    }
}

/// Start multi-node mode with proxy validation
///
/// # Arguments
/// * `env` - The environment to connect to.
/// * `config_file_path` - Path to the multi-node configuration file.
/// * `headless` - If true, runs without the terminal UI.
/// * `max_threads` - Optional maximum number of threads to use for proving per node.
/// * `batch_size` - Optional batch size for processing nodes.
/// * `batch_delay` - Optional delay between batches in milliseconds.
async fn start_multi_node(
    env: Environment,
    config_file_path: String,
    start_line: Option<usize>,
    end_line: Option<usize>,
    _headless: bool,
    max_threads: Option<u32>,
    batch_size: Option<usize>,
    batch_delay: Option<u64>,
    max_concurrent: Option<usize>,
    memory_debug: bool,
) -> Result<(), Box<dyn Error>> {
    use crate::config::MultiNodeConfig;
    use std::path::Path;

    // Load multi-node configuration with optional line range
    let config_path = Path::new(&config_file_path);
    let mut multi_config = MultiNodeConfig::load_from_csv_file_with_range(config_path, start_line, end_line)?;

    println!("🚀 Starting {} nodes from: {}", multi_config.nodes.len(), config_file_path);

    // Override configuration with CLI parameters
    if let Some(threads) = max_threads {
        multi_config.max_threads = Some(threads);
    }
    if let Some(size) = batch_size {
        multi_config.batch_size = Some(size);
    }
    if let Some(delay) = batch_delay {
        multi_config.batch_delay_ms = Some(delay);
    }

    let total_nodes = multi_config.nodes.len();

    println!("Loaded {} node configurations", total_nodes);

    // Initialize global compute manager
    // Use command line override or calculated optimal batch size
    let max_concurrent_computations = max_concurrent.or_else(|| Some(multi_config.calculate_optimal_batch_size()));
    compute_manager::init_global_compute_manager(max_concurrent_computations);

    let compute_manager = compute_manager::get_global_compute_manager();
    println!("🧮 Compute Manager: {} concurrent computations allowed", compute_manager.max_concurrent());

    // 原版逻辑：立即提交，遇到429错误时重试

    // Initialize memory monitoring
    memory_monitor::init_global_memory_monitor();
    memory_monitor::start_memory_monitoring().await;

    // Initialize task expiration detector
    task_expiration_detector::init_global_detector();
    if memory_debug {
        println!("🧠 Memory debugging enabled");
        unsafe {
            std::env::set_var("MEMORY_DEBUG", "1");
        }
        memory_monitor::log_memory_usage("Startup-Debug").await;
    }

    // Start periodic memory cleanup task (reduced frequency)
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(std::time::Duration::from_secs(600)); // Every 10 minutes
        loop {
            interval.tick().await;
            memory_monitor::force_cleanup("Periodic").await;
        }
    });

    println!("💾 System memory: {:.1}GB", crate::system::total_memory_gb());

    // Validate that we have at least one node
    if multi_config.nodes.is_empty() {
        return Err("No valid node configurations found in the config file".into());
    }

    println!("\n🚀 Starting {} nodes simultaneously...", multi_config.nodes.len());
    println!("⚡ All nodes will start immediately, but computations are limited to {} concurrent", compute_manager.max_concurrent());
    println!();

    // Start all nodes simultaneously
    let mut all_handles = Vec::new();

    for (node_index, node) in multi_config.nodes.iter().enumerate() {
        let node_display_id = node_index + 1;

        // 简化节点启动日志
        if let Some(proxy) = &node.proxy {
            println!("🚀 Node-{} (ID: {}) via proxy {}:{}", node_display_id, node.node_id, proxy.ip, proxy.port);
        } else {
            println!("🚀 Node-{} (ID: {})", node_display_id, node.node_id);
        }

        // Create a handle for this node
        let node_config = node.clone();
        let env_clone = env.clone();

        // Use conservative thread allocation for multi-node mode
        let node_threads = match multi_config.max_threads {
            Some(threads) => Some(threads.min(2)), // Cap at 2 threads per node
            None => Some(1), // Default to 1 thread per node
        };

        let handle = tokio::spawn(async move {
            // Start the real node with full prover functionality
            start_real_node(node_config, env_clone, node_threads).await
        });

        all_handles.push(handle);

        // Small delay between individual node starts to avoid overwhelming the system
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    }

    println!("\n✅ All {} nodes have been started!", multi_config.nodes.len());
    println!("🔄 Nodes are running in background tasks");
    println!("🧮 Computations are limited to {} concurrent to prevent system overload", compute_manager.max_concurrent());
    println!("📊 Use Ctrl+C to stop all nodes");

    // Wait for all nodes to complete (or until interrupted)
    println!("\n⏳ Waiting for nodes to complete...");
    for (index, handle) in all_handles.into_iter().enumerate() {
        match handle.await {
            Ok(result) => {
                match result {
                    Ok(_) => println!("✅ Node-{} completed successfully", index + 1),
                    Err(e) => println!("❌ Node-{} failed: {}", index + 1, e),
                }
            }
            Err(e) => println!("❌ Node-{} task failed: {}", index + 1, e),
        }
    }

    println!("🏁 All nodes stopped");
    Ok(())
}







/// Start a real node with proper prover functionality and soft restart capability
async fn start_real_node(
    node_config: crate::config::NodeConfig,
    env: Environment,
    max_threads: Option<u32>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {


    // Main restart loop - allows soft restart when tasks expire
    let mut restart_count = 0;
    loop {
        if restart_count > 0 {
            println!("    🔄 Soft restarting Node {} (restart #{})...", node_config.node_id, restart_count);
            // Wait a bit before restarting to avoid rapid restart loops
            tokio::time::sleep(std::time::Duration::from_secs(5)).await;
        }

        match run_node_instance(&node_config, &env, max_threads).await {
            Ok(should_restart) => {
                if should_restart {
                    restart_count += 1;
                    if restart_count > 10 {
                        println!("    ❌ Node {} exceeded maximum restart attempts", node_config.node_id);
                        break;
                    }
                    continue; // Restart the node
                } else {
                    break; // Normal shutdown
                }
            }
            Err(e) => {
                println!("    ❌ Node {} failed: {}", node_config.node_id, e);
                return Err(e);
            }
        }
    }

    println!("    🏁 Node {} completed successfully", node_config.node_id);
    Ok(())
}

/// Run a single instance of a node (can be restarted)
async fn run_node_instance(
    node_config: &crate::config::NodeConfig,
    env: &Environment,
    max_threads: Option<u32>,
) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
    use crate::prover_runtime::start_authenticated_workers;
    use ed25519_dalek::SigningKey;
    use tokio::sync::broadcast;
    use rand_core::OsRng;

    // Create orchestrator client with proxy support if configured
    let orchestrator_client = match &node_config.proxy {
        Some(proxy) => {
            match OrchestratorClient::new_with_proxy(env.clone(), Some(proxy)) {
                Ok(client) => client,
                Err(e) => {
                    println!("    ❌ Failed to create orchestrator client with proxy: {}", e);
                    return Err(format!("Proxy configuration error: {}", e).into());
                }
            }
        }
        None => OrchestratorClient::new(env.clone()),
    };

    // Create signing key for the prover
    let mut csprng = OsRng;
    let signing_key: SigningKey = SigningKey::generate(&mut csprng);

    // For multi-node mode, be more conservative with thread allocation
    // Default to 1 thread per node to avoid resource conflicts
    let num_workers: usize = max_threads.unwrap_or(1).clamp(1, 4) as usize;

    // Create shutdown and restart channels
    let (shutdown_sender, _) = broadcast::channel(1);
    let (restart_sender, mut restart_receiver) = broadcast::channel(1);

    // Set the restart sender in the task expiration detector for this specific node
    task_expiration_detector::set_restart_sender(node_config.node_id, restart_sender).await;

    // Get client_id for analytics
    let client_id = match orchestrator_client.get_node(&node_config.node_id.to_string()).await {
        Ok(wallet_address) => wallet_address,
        Err(_) => "anonymous".to_string(),
    };

    println!("    ✅ Node {} initialized successfully", node_config.node_id);

    // Start authenticated workers (this is the real prover logic!)
    let (mut event_receiver, mut join_handles) = start_authenticated_workers(
        node_config.node_id,
        signing_key,
        orchestrator_client,
        num_workers,
        shutdown_sender.subscribe(),
        env.clone(),
        client_id,
        None, // max_tasks - no limit for multi-node mode
    ).await;

    println!("    🔄 Node {} workers started, processing tasks...", node_config.node_id);

    // Set up Ctrl+C handler for this node
    let shutdown_sender_clone = shutdown_sender.clone();
    let node_id = node_config.node_id;
    tokio::spawn(async move {
        if tokio::signal::ctrl_c().await.is_ok() {
            println!("    🛑 Shutdown signal received for Node {}", node_id);
            let _ = shutdown_sender_clone.send(());
        }
    });

    // Run in headless mode: log events to console
    let mut shutdown_receiver = shutdown_sender.subscribe();
    let mut should_restart = false;

    loop {
        tokio::select! {
            Some(event) = event_receiver.recv() => {
                println!("    [Node {}] {}", node_config.node_id, event);
            }
            _ = shutdown_receiver.recv() => {
                println!("    🛑 Node {} shutting down...", node_config.node_id);
                break;
            }
            _ = restart_receiver.recv() => {
                println!("    🔄 Node {} received soft restart signal due to task expiration", node_config.node_id);
                should_restart = true;
                // Send shutdown signal to all workers
                let _ = shutdown_sender.send(());
                break;
            }
        }
    }

    // Wait for all worker handles to complete
    println!("    ⏳ Waiting for Node {} workers to finish...", node_config.node_id);
    for handle in join_handles.drain(..) {
        let _ = handle.await;
    }

    // Return whether we should restart
    Ok(should_restart)
}
