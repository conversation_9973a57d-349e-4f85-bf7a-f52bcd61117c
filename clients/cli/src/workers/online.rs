//! Online Workers
//!
//! Handles network-dependent operations including:
//! - Task fetching from the orchestrator
//! - Proof submission to the orchestrator
//! - Network error handling with exponential backoff

use crate::analytics::{
    track_got_task, track_proof_accepted, track_proof_submission_error,
    track_proof_submission_success,
};
use crate::consts::prover::{
    BACKOFF_DURATION, CONSECUTIVE_404_THRESHOLD, CONSECUTIVE_404_WINDOW, LOW_WATER_MARK,
    QUEUE_LOG_INTERVAL, TASK_QUEUE_SIZE, FETCH_TASK_DELAY_TIME,
};
use crate::environment::Environment;
use crate::error_classifier::{ErrorClassifier, LogLevel};
use crate::events::Event;
use crate::orchestrator::Orchestrator;
use crate::orchestrator::error::OrchestratorError;
use crate::task::Task;
use crate::task_cache::TaskCache;
use crate::task_expiration_detector;
use ed25519_dalek::{SigningKey, Verifying<PERSON><PERSON>};
use nexus_sdk::stwo::seq::Proof;
use sha3::{Digest, Keccak256};
use std::time::Duration;
use tokio::sync::{broadcast, mpsc};
use tokio::task::JoinHandle;

/// Result of a proof generation, with pre-serialized proof data for memory efficiency
pub struct ProofResult {
<<<<<<< HEAD
    pub proof_bytes: Vec<u8>,  // 预序列化的证明数据，避免重复序列化
    pub proof_hash: String,    // 证明哈希值
    pub combined_hash: String, // 组合哈希值
=======
    pub proof: Proof,
    pub combined_hash: String,
    pub individual_proof_hashes: Vec<String>,
>>>>>>> 6f911be0982932b0328e8aa72cff6bdfd3f4334d
}

/// Helper to send events with consistent error handling
async fn send_event(
    event_sender: &mpsc::Sender<Event>,
    message: String,
    event_type: crate::events::EventType,
    log_level: LogLevel,
) {
    let _ = event_sender
        .send(Event::task_fetcher_with_level(
            message, event_type, log_level,
        ))
        .await;
}

/// Helper to send proof submission events with consistent error handling
async fn send_proof_event(
    event_sender: &mpsc::Sender<Event>,
    message: String,
    event_type: crate::events::EventType,
    log_level: LogLevel,
) {
    let _ = event_sender
        .send(Event::proof_submitter_with_level(
            message, event_type, log_level,
        ))
        .await;
}

// =============================================================================
// TASK FETCH STATE
// =============================================================================

/// State for managing task fetching behavior with smart backoff and timing
pub struct TaskFetchState {
    last_fetch_time: std::time::Instant,
    backoff_duration: Duration,
    pub error_classifier: ErrorClassifier,
    // 404 error tracking for task expiration detection
    consecutive_404_count: u32,
    last_404_time: Option<std::time::Instant>,
    // Queue logging state
    last_queue_log_time: std::time::Instant,
    queue_log_interval: Duration,
}

impl TaskFetchState {
    pub fn new() -> Self {
        Self {
            last_fetch_time: std::time::Instant::now()
                - Duration::from_millis(BACKOFF_DURATION + 1000), // Allow immediate first fetch
            backoff_duration: Duration::from_millis(BACKOFF_DURATION), // Start with 120 second backoff
            error_classifier: ErrorClassifier::new(),
            consecutive_404_count: 0,
            last_404_time: None,
            last_queue_log_time: std::time::Instant::now(),
            queue_log_interval: Duration::from_millis(QUEUE_LOG_INTERVAL),
        }
    }

    // =========================================================================
    // QUERY METHODS
    // =========================================================================

    /// Check if enough time has passed since last fetch attempt (respects backoff)
    pub fn can_fetch_now(&self) -> bool {
        self.last_fetch_time.elapsed() >= self.backoff_duration
    }

    /// Get current backoff duration
    pub fn backoff_duration(&self) -> Duration {
        self.backoff_duration
    }

    /// Check if we should fetch tasks (combines queue level and backoff timing)
    pub fn should_fetch(&self, tasks_in_queue: usize) -> bool {
        tasks_in_queue < LOW_WATER_MARK && self.can_fetch_now()
    }

    // =========================================================================
    // MUTATION METHODS
    // =========================================================================

    /// Record that a fetch attempt was made (updates timing)
    pub fn record_fetch_attempt(&mut self) {
        self.last_fetch_time = std::time::Instant::now();
    }

    /// Set backoff duration from server's Retry-After header (in seconds)
    /// Respects server's exact timing for rate limit compliance
    pub fn set_backoff_from_server(&mut self, retry_after_seconds: u32) {
        self.backoff_duration =
            Duration::from_secs(retry_after_seconds as u64 + FETCH_TASK_DELAY_TIME);
    }

    /// Increase backoff duration for error handling (exponential backoff)
    pub fn increase_backoff_for_error(&mut self) {
        self.backoff_duration = std::cmp::min(
            self.backoff_duration * 2,
            Duration::from_millis(BACKOFF_DURATION * 2),
        );
    }

    /// Reset backoff duration to default (called after successful operations)
    pub fn reset_backoff(&mut self) {
        self.backoff_duration = Duration::from_millis(BACKOFF_DURATION);
    }

    /// Record a 404 error for task expiration detection
    pub fn record_404_error(&mut self) {
        let now = std::time::Instant::now();

        // Check if this 404 is within the time window of the last one
        if let Some(last_404) = self.last_404_time {
            if now.duration_since(last_404) <= Duration::from_millis(CONSECUTIVE_404_WINDOW) {
                self.consecutive_404_count += 1;
            } else {
                // Reset count if too much time has passed
                self.consecutive_404_count = 1;
            }
        } else {
            self.consecutive_404_count = 1;
        }

        self.last_404_time = Some(now);
    }

    /// Check if we should clear task queues due to consecutive 404 errors
    pub fn should_clear_queues(&self) -> bool {
        self.consecutive_404_count >= CONSECUTIVE_404_THRESHOLD
    }

    /// Reset 404 error tracking (called after successful operations)
    pub fn reset_404_tracking(&mut self) {
        self.consecutive_404_count = 0;
        self.last_404_time = None;
    }

    /// Check if enough time has passed to log queue status
    pub fn should_log_queue_status(&self) -> bool {
        self.last_queue_log_time.elapsed() >= self.queue_log_interval
    }

    /// Record that queue status was logged
    pub fn record_queue_log(&mut self) {
        self.last_queue_log_time = std::time::Instant::now();
    }
}

/// Simple task fetcher: get one task at a time when queue is low.
#[allow(clippy::too_many_arguments)]
pub async fn fetch_prover_tasks(
    node_id: u64,
    verifying_key: VerifyingKey,
    orchestrator_client: Box<dyn Orchestrator>,
    sender: mpsc::Sender<Task>,
    event_sender: mpsc::Sender<Event>,
    mut shutdown: broadcast::Receiver<()>,
    recent_tasks: TaskCache,
    environment: Environment,
    client_id: String,
) {
    let mut state = TaskFetchState::new();

    loop {
        tokio::select! {
            _ = shutdown.recv() => break,
            _ = tokio::time::sleep(Duration::from_millis(500)) => {
                let tasks_in_queue = TASK_QUEUE_SIZE - sender.capacity();

                // Log queue status periodically (只在调试模式下)
                if std::env::var("MEMORY_DEBUG").is_ok() && state.should_log_queue_status() {
                    state.record_queue_log();
                    log_queue_status(&event_sender, tasks_in_queue, &state).await;
                }

                // Simple condition: fetch when queue is low and backoff time has passed
                if state.should_fetch(tasks_in_queue) {
                    if let Err(should_return) = fetch_single_task(
                        &*orchestrator_client,
                        &node_id,
                        verifying_key,
                        &sender,
                        &event_sender,
                        &recent_tasks,
                        &mut state,
                        &environment,
                        &client_id,
                    ).await {
                        if should_return {
                            return;
                        }
                    }
                }
            }
        }
    }
}

/// Handle successful task fetch: duplicate check, caching, and queue management
#[allow(clippy::too_many_arguments)]
async fn handle_task_success(
    task: Task,
    sender: &mpsc::Sender<Task>,
    event_sender: &mpsc::Sender<Event>,
    recent_tasks: &TaskCache,
    state: &mut TaskFetchState,
    environment: &Environment,
    client_id: &str,
    node_id: u64,
) -> Result<(), bool> {
    // Check for duplicate
    if recent_tasks.contains(&task.task_id).await {
        handle_duplicate_task(event_sender, state).await;
        return Ok(());
    }

    // Process the new task
    process_new_task(
        task,
        sender,
        event_sender,
        recent_tasks,
        state,
        environment,
        client_id,
        node_id,
    )
    .await
}

/// Handle duplicate task detection
async fn handle_duplicate_task(event_sender: &mpsc::Sender<Event>, state: &mut TaskFetchState) {
    state.increase_backoff_for_error();
    send_event(
        event_sender,
        format!(
            "Task was duplicate - backing off for {}s",
            state.backoff_duration().as_secs()
        ),
        crate::events::EventType::Refresh,
        LogLevel::Warn,
    )
    .await;
}

/// Process a new (non-duplicate) task: cache, queue, analytics, and logging
#[allow(clippy::too_many_arguments)]
async fn process_new_task(
    task: Task,
    sender: &mpsc::Sender<Task>,
    event_sender: &mpsc::Sender<Event>,
    recent_tasks: &TaskCache,
    state: &mut TaskFetchState,
    environment: &Environment,
    client_id: &str,
    node_id: u64,
) -> Result<(), bool> {
    // Add to cache and queue
    recent_tasks.insert(task.task_id.clone()).await;

    if sender.send(task.clone()).await.is_err() {
        send_event(
            event_sender,
            "Task queue is closed".to_string(),
            crate::events::EventType::Shutdown,
            LogLevel::Error,
        )
        .await;
        return Err(true); // Signal shutdown
    }

    // Track analytics (non-blocking)
    tokio::spawn(track_got_task(
        task,
        environment.clone(),
        client_id.to_string(),
    ));

    // Success: reset backoff and log queue status
    state.reset_backoff();

    // Reset 404 error tracking on successful task fetch
    task_expiration_detector::reset_404_tracking(node_id).await;

    log_successful_task_addition(sender, event_sender).await;

    Ok(())
}

/// Handle fetch timeout with backoff and logging
async fn handle_fetch_timeout(
    timeout_duration: Duration,
    event_sender: &mpsc::Sender<Event>,
    state: &mut TaskFetchState,
) {
    state.increase_backoff_for_error();
    send_event(
        event_sender,
        format!("Fetch timeout after {}s", timeout_duration.as_secs()),
        crate::events::EventType::Error,
        LogLevel::Warn,
    )
    .await;
}

/// Perform task fetch with timeout
async fn fetch_task_with_timeout(
    orchestrator_client: &dyn Orchestrator,
    node_id: &u64,
    verifying_key: VerifyingKey,
    timeout_duration: Duration,
) -> Result<Result<Task, OrchestratorError>, tokio::time::error::Elapsed> {
    let node_id_str = node_id.to_string();
    let fetch_future = orchestrator_client.get_proof_task(&node_id_str, verifying_key);
    tokio::time::timeout(timeout_duration, fetch_future).await
}

/// Simple task fetcher: get one task, prove, submit - perfect 1-2-3 flow
#[allow(clippy::too_many_arguments)]
async fn fetch_single_task(
    orchestrator_client: &dyn Orchestrator,
    node_id: &u64,
    verifying_key: VerifyingKey,
    sender: &mpsc::Sender<Task>,
    event_sender: &mpsc::Sender<Event>,
    recent_tasks: &TaskCache,
    state: &mut TaskFetchState,
    environment: &Environment,
    client_id: &str,
) -> Result<(), bool> {
    // Record fetch attempt and send initial event
    state.record_fetch_attempt();

    // Send step 1 message (official format)
    send_event(
        event_sender,
        "Step 1 of 4: Requesting task...".to_string(),
        crate::events::EventType::Refresh,
        LogLevel::Info,
    )
    .await;

    // 只在调试模式下显示详细的任务获取信息
    if std::env::var("MEMORY_DEBUG").is_ok() {
        send_event(
            event_sender,
            "🔍 Fetching task...".to_string(),
            crate::events::EventType::Refresh,
            LogLevel::Debug,
        ).await;
    }

    // Fetch task with timeout
    let timeout_duration = Duration::from_secs(60);
    match fetch_task_with_timeout(
        orchestrator_client,
        node_id,
        verifying_key,
        timeout_duration,
    )
    .await
    {
        Ok(fetch_result) => match fetch_result {
            Ok(task) => {
                handle_task_success(
                    task,
                    &sender,
                    &event_sender,
                    &recent_tasks,
                    state,
                    &environment,
                    &client_id,
                    *node_id,
                )
                .await
            }
            Err(e) => {
                handle_fetch_error(e, event_sender, state).await;
                Ok(())
            }
        },
        Err(_timeout) => {
            handle_fetch_timeout(timeout_duration, event_sender, state).await;
            Ok(())
        }
    }
}

/// Handle fetch errors with appropriate backoff
async fn handle_fetch_error(
    error: OrchestratorError,
    event_sender: &mpsc::Sender<Event>,
    state: &mut TaskFetchState,
) {
    match error {
        OrchestratorError::Http {
            status: 429,
            ref headers,
            ..
        } => {
            // 只在调试模式下显示429详细信息
            if std::env::var("MEMORY_DEBUG").is_ok() {
                send_event(
                    event_sender,
                    format!("⚠️ Rate limit, retry-after: {:?}", headers.get("retry-after")),
                    crate::events::EventType::Refresh,
                    LogLevel::Debug,
                )
                .await;
            }

            if let Some(retry_after_seconds) = error.get_retry_after_seconds() {
                state.set_backoff_from_server(retry_after_seconds);
                send_event(
                    event_sender,
                    format!("Fetch rate limited - retrying in {}s", retry_after_seconds),
                    crate::events::EventType::Waiting,
                    LogLevel::Warn,
                )
                .await;
            } else {
                // This shouldn't happen with a properly configured server
                state.increase_backoff_for_error();
                send_event(
                    event_sender,
                    format!("⚠️ Rate limit ({}s)", state.backoff_duration().as_secs()),
                    crate::events::EventType::Error,
                    LogLevel::Warn,
                )
                .await;
            }
        }
        OrchestratorError::Http {
            status, headers, ..
        } => {
            // Print out all headers
            let mut msg = String::new();
            for (key, value) in headers {
                msg.push_str(&format!("{}: {}\n", key, value));
            }
            send_event(
                event_sender,
                format!("HTTP Error {}: {}", status, msg),
                crate::events::EventType::Error,
                LogLevel::Warn,
            )
            .await;
        }
        _ => {
            state.increase_backoff_for_error();
            let log_level = state.error_classifier.classify_fetch_error(&error);
            // 简化错误重试日志，只在调试模式显示详细信息
            let message = if std::env::var("MEMORY_DEBUG").is_ok() {
                format!("❌ {}, retry in {}s", error, state.backoff_duration().as_secs())
            } else {
                format!("❌ Fetch error ({}s)", state.backoff_duration().as_secs())
            };

            let event = Event::task_fetcher_with_level(
                message,
                crate::events::EventType::Error,
                log_level,
            );
            if event.should_display() {
                let _ = event_sender.send(event).await;
            }
        }
    }
}

/// Submits proofs to the orchestrator
#[allow(clippy::too_many_arguments)]
pub async fn submit_proofs(
    node_id: u64,
    signing_key: SigningKey,
    orchestrator: Box<dyn Orchestrator>,
    num_workers: usize,
    mut results: mpsc::Receiver<(Task, ProofResult)>,
    event_sender: mpsc::Sender<Event>,
    mut shutdown: broadcast::Receiver<()>,
    completed_tasks: TaskCache,
    environment: Environment,
    client_id: String,
    max_tasks: Option<u32>,
) -> JoinHandle<()> {
    tokio::spawn(async move {
        let mut tasks_processed = 0;
        loop {
            tokio::select! {
                maybe_item = results.recv() => {
                    match maybe_item {
                        Some((task, proof_result)) => {
                            process_proof_submission(
                                task,
                                proof_result.proof_bytes,
                                proof_result.proof_hash,
                                proof_result.combined_hash,
                                proof_result.individual_proof_hashes,
                                &*orchestrator,
                                &signing_key,
                                num_workers,
                                &event_sender,
                                &completed_tasks,
                                &environment,
                                &client_id,
                                node_id,
                                &mut tasks_processed,
                            ).await;

                            // Check if we've reached the max tasks limit
                            if let Some(max) = max_tasks {
                                if tasks_processed >= max {
                                    // Reached max tasks, exit cleanly
                                    std::process::exit(0);
                                }
                            }
                        }
                        None => break,
                    }
                }

                _ = shutdown.recv() => break,
            }
        }
    })
}

/// Check if task was already submitted (successfully or failed)
async fn check_duplicate_submission(
    task: &Task,
    submitted_tasks: &TaskCache,
    event_sender: &mpsc::Sender<Event>,
) -> bool {
    if submitted_tasks.contains(&task.task_id).await {
        let msg = format!(
            "Ignoring proof for previously processed task {}",
            task.task_id
        );
        send_proof_event(
            event_sender,
            msg,
            crate::events::EventType::Error,
            LogLevel::Warn,
        )
        .await;
        return true; // Is duplicate
    }
    false // Not duplicate
}

/// Generate proof hash from combined hash or by computing from proof
fn generate_proof_hash(proof: &Proof, combined_hash: String) -> String {
    if !combined_hash.is_empty() {
        combined_hash
    } else {
        // Serialize proof and generate hash
        let proof_bytes = postcard::to_allocvec(proof).expect("Failed to serialize proof");
        format!("{:x}", Keccak256::digest(&proof_bytes))
    }
}

/// Submit proof to orchestrator using pre-serialized proof data (原版逻辑：立即提交)
#[allow(clippy::too_many_arguments)]
async fn submit_proof_to_orchestrator(
    task: &Task,
    proof_bytes: Vec<u8>,
    proof_hash: &str,
    individual_proof_hashes: &[String],
    orchestrator: &dyn Orchestrator,
    signing_key: &SigningKey,
    num_workers: usize,
    event_sender: &mpsc::Sender<Event>,
    completed_tasks: &TaskCache,
    environment: &Environment,
    client_id: &str,
    node_id: u64,
    tasks_processed: &mut u32,
) {
    // Send submitting message
    send_proof_event(
        event_sender,
        "Step 3 of 4: Submitting (Sending your proof to the network)...".to_string(),
        crate::events::EventType::Waiting,
        LogLevel::Info,
    )
    .await;

    // Submit to orchestrator using pre-serialized proof data
    match orchestrator
        .submit_proof(
            &task.task_id,
            proof_hash,
            proof_bytes.clone(), // Clone for submission, keep original for potential retry
            signing_key.clone(),
            num_workers,
            task.task_type,
            individual_proof_hashes,
        )
        .await
    {
        Ok(_) => {
            // Track analytics for proof submission success (non-blocking)
            tokio::spawn(track_proof_submission_success(
                task.clone(),
                environment.clone(),
                client_id.to_string(),
            ));

            // Force memory cleanup after successful proof submission
            crate::memory_monitor::force_cleanup(&format!("Submit-{}", task.task_id)).await;

            handle_submission_success(task, event_sender, completed_tasks, environment, client_id, node_id)
                .await;

            // Additional memory cleanup after successful submission
            crate::memory_monitor::cleanup_if_pressure(&format!("Success-{}", task.task_id)).await;

            // Increment task counter
            *tasks_processed += 1;
        }
        Err(e) => {
            // Handle submission error with potential retry for 429 errors
            handle_submission_error_with_retry(
                task,
                e,
                event_sender,
                completed_tasks,
                environment,
                client_id,
                orchestrator,
                proof_bytes,
                proof_hash,
                signing_key,
                num_workers,
                node_id,
            )
            .await;
        }
    }
}

/// Process a single proof submission with pre-serialized proof data
#[allow(clippy::too_many_arguments)]
async fn process_proof_submission(
    task: Task,
<<<<<<< HEAD
    proof_bytes: Vec<u8>,
    proof_hash: String,
    _combined_hash: String,
=======
    proof: Proof,
    combined_hash: String,
    individual_proof_hashes: Vec<String>,
>>>>>>> 6f911be0982932b0328e8aa72cff6bdfd3f4334d
    orchestrator: &dyn Orchestrator,
    signing_key: &SigningKey,
    num_workers: usize,
    event_sender: &mpsc::Sender<Event>,
    completed_tasks: &TaskCache,
    environment: &Environment,
    client_id: &str,
    node_id: u64,
    tasks_processed: &mut u32,
) {
    // Check for duplicate submissions
    if check_duplicate_submission(&task, completed_tasks, event_sender).await {
        return; // Skip duplicate task
    }

    // 证明哈希已经预计算，无需重新生成

    // Submit to orchestrator and handle result
    submit_proof_to_orchestrator(
        &task,
        proof_bytes,
        &proof_hash,
        &individual_proof_hashes,
        orchestrator,
        signing_key,
        num_workers,
        event_sender,
        completed_tasks,
        environment,
        client_id,
        node_id,
        tasks_processed,
    )
    .await;
}

/// Handle successful proof submission
async fn handle_submission_success(
    task: &Task,
    event_sender: &mpsc::Sender<Event>,
    completed_tasks: &TaskCache,
    environment: &Environment,
    client_id: &str,
    node_id: u64,
) {
    completed_tasks.insert(task.task_id.clone()).await;

    // Reset 404 error tracking on successful submission
    task_expiration_detector::reset_404_tracking(node_id).await;

    // Use official step 4 message format
    let msg = "Step 4 of 4: Submitted! ≈300 points will be added soon\n".to_string();
    // Track analytics for proof acceptance (non-blocking)
    tokio::spawn(track_proof_accepted(
        task.clone(),
        environment.clone(),
        client_id.to_string(),
    ));

    send_proof_event(
        event_sender,
        msg,
        crate::events::EventType::Success,
        LogLevel::Info,
    )
    .await;
}

/// Handle submission error with retry logic for 429 errors (原版逻辑)
#[allow(clippy::too_many_arguments)]
async fn handle_submission_error_with_retry(
    task: &Task,
    error: OrchestratorError,
    event_sender: &mpsc::Sender<Event>,
    completed_tasks: &TaskCache,
    environment: &Environment,
    client_id: &str,
    orchestrator: &dyn Orchestrator,
    proof_bytes: Vec<u8>,
    proof_hash: &str,
    signing_key: &SigningKey,
    num_workers: usize,
    node_id: u64,
) {
    match error {
        OrchestratorError::Http { status: 429, ref headers, .. } => {
            // 429 Rate Limit - 按原版逻辑重试
            if std::env::var("MEMORY_DEBUG").is_ok() {
                let _ = event_sender
                    .send(Event::proof_submitter_with_level(
                        format!("⚠️ 429 Rate limit, retry-after: {:?}", headers.get("retry-after")),
                        crate::events::EventType::Refresh,
                        crate::error_classifier::LogLevel::Debug,
                    ))
                    .await;
            }

            // 获取重试延迟时间
            let retry_delay = if let Some(retry_after) = headers.get("retry-after") {
                retry_after.parse::<u64>().unwrap_or(120) // 默认2分钟
            } else {
                120 // 默认2分钟
            };

            let _ = event_sender
                .send(Event::proof_submitter_with_level(
                    format!("⏳ Rate limited, retrying in {}s", retry_delay),
                    crate::events::EventType::Refresh,
                    crate::error_classifier::LogLevel::Info,
                ))
                .await;

            // 等待重试延迟
            tokio::time::sleep(Duration::from_secs(retry_delay)).await;

            // 重试提交 (使用 Box::pin 避免递归问题)
            // Note: For retry, we create a dummy tasks_processed counter since it's not used in retry context
            let mut dummy_tasks_processed = 0;
            Box::pin(submit_proof_to_orchestrator(
                task,
                proof_bytes,
                proof_hash,
                orchestrator,
                signing_key,
                num_workers,
                event_sender,
                completed_tasks,
                environment,
                client_id,
                node_id,
                &mut dummy_tasks_processed,
            ))
            .await;
        }
        _ => {
            // 其他错误，使用原版错误处理
            handle_submission_error(task, error, event_sender, completed_tasks, environment, client_id, node_id).await;
        }
    }
}

/// Handle proof submission errors
async fn handle_submission_error(
    task: &Task,
    error: OrchestratorError,
    event_sender: &mpsc::Sender<Event>,
    completed_tasks: &TaskCache,
    environment: &Environment,
    client_id: &str,
    node_id: u64,
) {
    let (msg, status_code) = match error {
        OrchestratorError::Http {
            status,
            ref message,
            ..
        } => {
            // Handle 404 errors specially ONLY for proof submission - they indicate task expiration
            // Other 404 errors (version check, task fetch, etc.) should not trigger restart
            if status == 404 {
                let should_restart = task_expiration_detector::record_404_error(node_id).await;
                if should_restart {
                    send_proof_event(
                        event_sender,
                        format!("🔄 Detected {} consecutive proof submission 404 errors - triggering soft restart to clear all state",
                               crate::consts::prover::CONSECUTIVE_404_THRESHOLD),
                        crate::events::EventType::Refresh,
                        LogLevel::Warn,
                    ).await;

                    // Clear the completed tasks cache immediately
                    completed_tasks.clear().await;

                    // Force memory cleanup
                    crate::memory_monitor::force_cleanup("404-SoftRestart").await;

                    // The restart signal has already been sent by record_404_error()
                }
            }

            (
                format!(
                    "Failed to submit proof for task {}. Status: {}, Message: {}",
                    task.task_id, status, message
                ),
                Some(status),
            )
        },
        e => (
            format!("Failed to submit proof for task {}: {}", task.task_id, e),
            None,
        ),
    };

    // Add to cache to prevent resubmission of failed proofs
    // Once a proof fails, we don't want to waste resources trying again
    completed_tasks.insert(task.task_id.clone()).await;

    // Track analytics for proof submission error (non-blocking)
    tokio::spawn(track_proof_submission_error(
        task.clone(),
        msg.clone(),
        status_code,
        environment.clone(),
        client_id.to_string(),
    ));

    send_proof_event(
        event_sender,
        msg.to_string(),
        crate::events::EventType::Error,
        LogLevel::Error,
    )
    .await;
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_set_backoff_from_server() {
        let mut state = TaskFetchState::new();

        // Test setting a reasonable retry time
        state.set_backoff_from_server(60);
        assert_eq!(
            state.backoff_duration,
            Duration::from_secs(60 + FETCH_TASK_DELAY_TIME)
        );

        // Test that longer retry times are respected (no capping)
        state.set_backoff_from_server(300); // 5 minutes
        assert_eq!(
            state.backoff_duration,
            Duration::from_secs(300 + FETCH_TASK_DELAY_TIME)
        );

        // Test zero retry time
        state.set_backoff_from_server(0);
        assert_eq!(
            state.backoff_duration,
            Duration::from_secs(FETCH_TASK_DELAY_TIME)
        );
    }

    #[test]
    fn test_server_retry_times_respected() {
        let mut state = TaskFetchState::new();

        // Test that very long retry times are respected
        state.set_backoff_from_server(3600); // 1 hour
        assert_eq!(
            state.backoff_duration,
            Duration::from_secs(3600 + FETCH_TASK_DELAY_TIME)
        );
    }
}

/// Log queue status for debugging
async fn log_queue_status(
    event_sender: &mpsc::Sender<Event>,
    tasks_in_queue: usize,
    state: &TaskFetchState,
) {
    let message = format!(
        "Queue status: {} tasks, backoff: {}s",
        tasks_in_queue,
        state.backoff_duration().as_secs()
    );
    send_event(
        event_sender,
        message,
        crate::events::EventType::Refresh,
        LogLevel::Debug,
    )
    .await;
}

/// Log successful task addition
async fn log_successful_task_addition(
    sender: &mpsc::Sender<Task>,
    event_sender: &mpsc::Sender<Event>,
) {
    let tasks_in_queue = TASK_QUEUE_SIZE - sender.capacity();
    let message = format!("Task added to queue (queue size: {})", tasks_in_queue);
    send_event(
        event_sender,
        message,
        crate::events::EventType::Success,
        LogLevel::Debug,
    )
    .await;
}
