name: ci

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - "**"
  workflow_dispatch:  # Allow manual triggering

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            clients/cli
            proto
            tests

      - name: Check system resources
        run: |
          echo "Available memory:"
          if [[ "$RUNNER_OS" == "Linux" ]]; then
            free -h
            echo "Total memory: $(free -m | awk 'NR==2{print $2}') MB"
            echo "Available memory: $(free -m | awk 'NR==2{print $7}') MB"
            if [ $(free -m | awk 'NR==2{print $2}') -lt 4096 ]; then
              echo "WARNING: Less than 4GB total memory available"
            fi
          elif [[ "$RUNNER_OS" == "macOS" ]]; then
            vm_stat | head -4
            echo "Total memory: $(sysctl -n hw.memsize | awk '{print int($1/1024/1024)}') MB"
            echo "Available memory: $(vm_stat | awk '/free/ {gsub(/\./, "", $3); print $3*4096/1024/1024}' | head -1) MB"
            if [ $(sysctl -n hw.memsize | awk '{print int($1/1024/1024)}') -lt 4096 ]; then
              echo "WARNING: Less than 4GB total memory available"
            fi
          elif [[ "$RUNNER_OS" == "Windows" ]]; then
            wmic computersystem get TotalPhysicalMemory | tail -1 | awk '{print int($1/1024/1024)}' | xargs -I {} echo "Total memory: {} MB"
            echo "Available memory: $(wmic OS get FreePhysicalMemory | tail -1 | awk '{print int($1/1024)}') MB"
            if [ $(wmic computersystem get TotalPhysicalMemory | tail -1 | awk '{print int($1/1024/1024)}') -lt 4096 ]; then
              echo "WARNING: Less than 4GB total memory available"
            fi
          fi

      # Install Rust toolchain used by CLI
      # When passing an explicit toolchain... use "dtolnay/rust-toolchain@master""
      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2025-04-06
          target: x86_64-unknown-linux-gnu
          components: rustfmt, clippy

      - name: Set up Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: ./clients/cli

      - name: Install Protobuf Compiler
        run: |
          sudo apt-get update
          sudo apt-get install -y protobuf-compiler

      - name: Check formatting
        working-directory: clients/cli
        run: cargo fmt --all -- --check

      - name: Run cargo clippy
        working-directory: clients/cli
        run: cargo clippy --release --no-deps --package nexus-network -- -D warnings

      - name: Build CLI
        working-directory: clients/cli
        run: cargo build --release

      # Unit tests for CLI functionality
      - name: Unit Tests
        working-directory: clients/cli
        run: cargo test --release --tests

      # Integration test against production orchestrator
      - name: Integration Test
        working-directory: clients/cli
        timeout-minutes: 5
        run: |
          if [ -n "${{ secrets.SMOKE_TEST_NODE_IDS }}" ]; then
            echo "Running integration test with secret node IDs"
            SMOKE_TEST_NODE_IDS="${{ secrets.SMOKE_TEST_NODE_IDS }}" ../../tests/integration_test.sh ./target/release/nexus-network --max-tasks 1
          else
            echo "Running integration test with fallback node IDs"
            ../../tests/integration_test.sh ./target/release/nexus-network --max-tasks 1
          fi

      - name: Ensure checked in generated files are up to date
        run: |
          if [ -n "$(git status --porcelain)" ]; then \
              echo "There are uncommitted changes in working tree after building."; \
              git status; \
              git --no-pager diff; \
              exit 1; \
          else \
              echo "Git working tree is clean"; \
          fi;
