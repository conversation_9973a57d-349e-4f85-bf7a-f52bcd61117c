# Nexus CLI 多节点配置示例
# 支持三种格式：
# 1. 纯节点ID（不使用代理）: nodeid
# 2. HTTP代理: *********************:port,nodeid
# 3. SOCKS5代理: socks5://user:pass@host:port,nodeid

# 不使用代理的节点
12345
67890
11111

# 使用HTTP代理的节点
http://user1:<EMAIL>:8080,22222
http://user2:<EMAIL>:3128,33333

# 使用SOCKS5代理的节点
socks5://user3:<EMAIL>:1080,44444
socks5://user4:<EMAIL>:1080,55555

# 混合配置示例
99999
http://proxyuser:<EMAIL>:8080,88888
socks5://socksuser:<EMAIL>:1080,77777
66666
