//! Application configuration.

use crate::environment::Environment;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::{fs, path::Path};
use url::Url;

/// Get the path to the Nexus config file, typically located at ~/.nexus/config.json.
pub fn get_config_path() -> Result<PathBuf, std::io::Error> {
    let home_path = home::home_dir().ok_or(std::io::Error::new(
        std::io::ErrorKind::NotFound,
        "Home directory not found",
    ))?;
    let config_path = home_path.join(".nexus").join("config.json");
    Ok(config_path)
}

/// Proxy type enumeration
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq)]
pub enum ProxyType {
    #[serde(rename = "socks5")]
    Socks5,
    #[serde(rename = "http")]
    Http,
    #[serde(rename = "http_url")]
    HttpUrl,
}

impl Default for ProxyType {
    fn default() -> Self {
        ProxyType::Socks5
    }
}

/// SOCKS5/HTTP proxy configuration for a single node
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq)]
pub struct ProxyConfig {
    #[serde(default)]
    pub proxy_type: ProxyType,
    pub ip: String,
    pub port: u16,
    pub username: Option<String>,
    pub password: Option<String>,
    pub full_url: Option<String>,  // Store complete HTTP URL when using HttpUrl type
}

/// Configuration for a single node with optional proxy
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq)]
pub struct NodeConfig {
    pub node_id: u64,
    pub proxy: Option<ProxyConfig>,
}

/// Multi-node configuration structure
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq)]
pub struct MultiNodeConfig {
    pub nodes: Vec<NodeConfig>,
    pub max_threads: Option<u32>,
    pub batch_size: Option<usize>,
    pub batch_delay_ms: Option<u64>,
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq, Eq)]
pub struct Config {
    /// Environment
    #[serde(default)]
    pub environment: String,

    /// The unique identifier for the node, UUIDv4 format. Empty when not yet registered.
    #[serde(default)]
    pub user_id: String,

    /// The wallet address associated with the user, typically an Ethereum address. Empty when not yet registered.
    #[serde(default)]
    pub wallet_address: String,

    /// The node's unique identifier, probably an integer. Empty when not yet registered.
    #[serde(default)]
    pub node_id: String,
}

impl Config {
    /// Create Config with the given node_id.
    pub fn new(
        user_id: String,
        wallet_address: String,
        node_id: String,
        environment: Environment,
    ) -> Self {
        Config {
            user_id,
            wallet_address,
            node_id,
            environment: environment.to_string(),
        }
    }

    /// Loads configuration from a JSON file at the given path.
    ///
    /// # Errors
    /// Returns an `std::io::Error` if reading from file fails or JSON is invalid.
    pub fn load_from_file(path: &Path) -> Result<Self, std::io::Error> {
        let buf = fs::read(path)?;
        let config: Config = serde_json::from_slice(&buf)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;
        Ok(config)
    }

    /// Saves the configuration to a JSON file at the given path.
    ///
    /// Directories will be created if they don't exist. This method overwrites existing files.
    ///
    /// # Errors
    /// Returns an `std::io::Error` if writing to file fails or serialization fails.
    pub fn save(&self, path: &Path) -> Result<(), std::io::Error> {
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent)?;
        }
        let json = serde_json::to_string_pretty(self).map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Serialization failed: {}", e),
            )
        })?;
        fs::write(path, json)?;
        Ok(())
    }

    /// Clear the node ID configuration file.
    pub fn clear_node_config(path: &Path) -> std::io::Result<()> {
        // Check that the path ends with config.json
        if !path.ends_with("config.json") {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                "Path must end with config.json",
            ));
        }

        // If no file exists, return OK
        if !path.exists() {
            println!("No config file found at {}", path.display());
            return Ok(());
        }

        // If the file exists, remove it
        fs::remove_file(path)
    }
}

impl MultiNodeConfig {
    /// Parse multi-node configuration from a CSV-like file
    /// Supports three formats:
    /// 1. Node ID only: nodeid (no proxy)
    /// 2. HTTP URL: *********************:port,nodeid
    /// 3. SOCKS5 URL: socks5://user:pass@host:port,nodeid or socks5h://user:pass@host:port,nodeid
    /// Lines starting with # are treated as comments
    pub fn load_from_csv_file(path: &Path) -> Result<Self, std::io::Error> {
        Self::load_from_csv_file_with_range(path, None, None)
    }

    /// Parse multi-node configuration from a CSV-like file with line range support
    ///
    /// # Arguments
    /// * `path` - Path to the CSV file
    /// * `start_line` - Optional start line number (1-based, inclusive)
    /// * `end_line` - Optional end line number (1-based, inclusive)
    ///
    /// # Examples
    /// ```
    /// // Load lines 5-10 from config file
    /// let config = MultiNodeConfig::load_from_csv_file_with_range(path, Some(5), Some(10))?;
    ///
    /// // Load from line 10 to end
    /// let config = MultiNodeConfig::load_from_csv_file_with_range(path, Some(10), None)?;
    ///
    /// // Load first 20 lines
    /// let config = MultiNodeConfig::load_from_csv_file_with_range(path, None, Some(20))?;
    /// ```
    pub fn load_from_csv_file_with_range(
        path: &Path,
        start_line: Option<usize>,
        end_line: Option<usize>
    ) -> Result<Self, std::io::Error> {
        let content = fs::read_to_string(path)?;
        let mut nodes = Vec::new();

        // Validate line range parameters
        if let (Some(start), Some(end)) = (start_line, end_line) {
            if start > end {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::InvalidInput,
                    format!("Start line ({}) cannot be greater than end line ({})", start, end),
                ));
            }
            if start == 0 {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::InvalidInput,
                    "Line numbers are 1-based, start line cannot be 0",
                ));
            }
        }

        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len();

        // Determine actual range to process
        let actual_start = start_line.unwrap_or(1).saturating_sub(1); // Convert to 0-based
        let actual_end = end_line.unwrap_or(total_lines).min(total_lines);

        // Validate range against file content
        if actual_start >= total_lines {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                format!("Start line ({}) exceeds file length ({})", start_line.unwrap_or(1), total_lines),
            ));
        }

        println!("📄 Reading config file: {} (lines {}-{} of {})",
            path.display(),
            actual_start + 1,
            actual_end,
            total_lines
        );

        for (index, line) in lines.iter().enumerate() {
            // Skip lines outside the specified range
            if index < actual_start || index >= actual_end {
                continue;
            }

            let line = line.trim();
            let line_num = index + 1; // Convert back to 1-based for error reporting

            // Skip empty lines and comments
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            // Detect format and parse accordingly
            let node_config = if Self::is_http_url_format(line) {
                Self::parse_http_url_format(line, line_num)?
            } else if Self::is_socks5_url_format(line) {
                Self::parse_socks5_url_format(line, line_num)?
            } else if Self::is_node_id_only_format(line) {
                Self::parse_node_id_only_format(line, line_num)?
            } else {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::InvalidData,
                    format!(
                        "Invalid format at line {}: expected 'nodeid' (no proxy), '*********************:port,nodeid', or 'socks5://user:pass@host:port,nodeid', got '{}'",
                        line_num, line
                    ),
                ));
            };

            nodes.push(node_config);
        }

        if nodes.is_empty() {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                "No valid node configurations found in file",
            ));
        }

        Ok(MultiNodeConfig {
            nodes,
            max_threads: None,
            batch_size: None,
            batch_delay_ms: None,
        })
    }

    /// Check if line is in HTTP URL format
    fn is_http_url_format(line: &str) -> bool {
        line.starts_with("http://") || line.starts_with("https://")
    }

    /// Check if line is in SOCKS5 URL format
    fn is_socks5_url_format(line: &str) -> bool {
        line.starts_with("socks5://") || line.starts_with("socks5h://")
    }

    /// Check if line is in node ID only format (just a number, no proxy)
    fn is_node_id_only_format(line: &str) -> bool {
        // Check if the line is just a number (node ID only)
        line.trim().parse::<u64>().is_ok()
    }

    /// Parse node ID only format: nodeid (no proxy)
    fn parse_node_id_only_format(line: &str, line_num: usize) -> Result<NodeConfig, std::io::Error> {
        let node_id = line.trim().parse::<u64>().map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid node ID at line {}: {}", line_num, e),
            )
        })?;

        Ok(NodeConfig {
            node_id,
            proxy: None, // No proxy configuration
        })
    }

    /// Parse HTTP URL format: *********************:port,nodeid
    fn parse_http_url_format(line: &str, line_num: usize) -> Result<NodeConfig, std::io::Error> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() != 2 {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!(
                    "Invalid HTTP URL format at line {}: expected '*********************:port,nodeid', got '{}'",
                    line_num, line
                ),
            ));
        }

        let url_str = parts[0].trim();
        let node_id_str = parts[1].trim();

        let node_id = node_id_str.parse::<u64>().map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid node_id at line {}: {}", line_num, e),
            )
        })?;

        let proxy_config = Self::parse_http_proxy_url(url_str, line_num)?;

        Ok(NodeConfig {
            node_id,
            proxy: Some(proxy_config),
        })
    }

    /// Parse SOCKS5 URL format: socks5://user:pass@host:port,nodeid or socks5h://user:pass@host:port,nodeid
    fn parse_socks5_url_format(line: &str, line_num: usize) -> Result<NodeConfig, std::io::Error> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() != 2 {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!(
                    "Invalid SOCKS5 URL format at line {}: expected 'socks5://user:pass@host:port,nodeid', got '{}'",
                    line_num, line
                ),
            ));
        }

        let url_str = parts[0].trim();
        let node_id_str = parts[1].trim();

        let node_id = node_id_str.parse::<u64>().map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid node_id at line {}: {}", line_num, e),
            )
        })?;

        let proxy_config = Self::parse_socks5_proxy_url(url_str, line_num)?;

        Ok(NodeConfig {
            node_id,
            proxy: Some(proxy_config),
        })
    }



    /// Parse HTTP proxy URL: *********************:port
    fn parse_http_proxy_url(url_str: &str, line_num: usize) -> Result<ProxyConfig, std::io::Error> {
        let url = Url::parse(url_str).map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid HTTP URL at line {}: {}", line_num, e),
            )
        })?;

        // Validate scheme
        if url.scheme() != "http" && url.scheme() != "https" {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid URL scheme at line {}: expected 'http' or 'https', got '{}'", line_num, url.scheme()),
            ));
        }

        // Extract host
        let host = url.host_str().ok_or_else(|| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Missing host in URL at line {}", line_num),
            )
        })?;

        // Extract port (default to 80 for http, 443 for https)
        let port = url.port().unwrap_or_else(|| {
            if url.scheme() == "https" { 443 } else { 80 }
        });

        // Extract username and password
        let username = if url.username().is_empty() {
            None
        } else {
            Some(url.username().to_string())
        };

        let password = url.password().map(|p| p.to_string());

        Ok(ProxyConfig {
            proxy_type: ProxyType::HttpUrl,
            ip: host.to_string(),
            port,
            username,
            password,
            full_url: Some(url_str.to_string()),
        })
    }

    /// Parse SOCKS5 proxy URL: socks5://user:pass@host:port or socks5h://user:pass@host:port
    fn parse_socks5_proxy_url(url_str: &str, line_num: usize) -> Result<ProxyConfig, std::io::Error> {
        let url = Url::parse(url_str).map_err(|e| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid SOCKS5 URL at line {}: {}", line_num, e),
            )
        })?;

        // Validate scheme
        if url.scheme() != "socks5" && url.scheme() != "socks5h" {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Invalid URL scheme at line {}: expected 'socks5' or 'socks5h', got '{}'", line_num, url.scheme()),
            ));
        }

        // Extract host
        let host = url.host_str().ok_or_else(|| {
            std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Missing host in URL at line {}", line_num),
            )
        })?;

        // Extract port (default to 1080 for SOCKS5)
        let port = url.port().unwrap_or(1080);

        // Extract username and password
        let username = if url.username().is_empty() {
            None
        } else {
            Some(url.username().to_string())
        };

        let password = url.password().map(|p| p.to_string());

        Ok(ProxyConfig {
            proxy_type: ProxyType::Socks5,
            ip: host.to_string(),
            port,
            username,
            password,
            full_url: Some(url_str.to_string()),
        })
    }

    /// Calculate optimal batch size based on system resources
    pub fn calculate_optimal_batch_size(&self) -> usize {
        if let Some(batch_size) = self.batch_size {
            return batch_size;
        }

        // Auto-calculate based on system memory and node count
        let total_nodes = self.nodes.len();
        let memory_gb = crate::system::total_memory_gb();

        // Optimized estimate based on real-world benchmarks:
        // Each node uses ~1.5-2GB during proof generation (Nexus zkVM data)
        let memory_per_node_gb = 2.0; // Realistic estimate based on testing

        // Use 75% of available memory for better utilization
        let usable_memory_gb = memory_gb * 0.75;

        // Calculate max concurrent nodes
        let max_concurrent = (usable_memory_gb / memory_per_node_gb) as usize;

        // Additional safety limits:
        // - Up to 24 concurrent nodes for better resource utilization
        // - At least 1 node
        // - Don't exceed total nodes
        let safe_max = std::cmp::min(max_concurrent, 24);
        std::cmp::max(1, std::cmp::min(safe_max, total_nodes))
    }

    /// Get batch delay in milliseconds
    pub fn get_batch_delay_ms(&self) -> u64 {
        self.batch_delay_ms.unwrap_or(2000) // Default 2 seconds
    }

    /// Get node batches for processing
    pub fn get_node_batches(&self) -> Vec<Vec<NodeConfig>> {
        let batch_size = self.calculate_optimal_batch_size();
        self.nodes.chunks(batch_size).map(|chunk| chunk.to_vec()).collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs::File;
    use std::io::Write;
    use tempfile::tempdir;

    /// Helper function to create a test configuration.
    fn get_config() -> Config {
        Config {
            environment: "test".to_string(),
            user_id: "test_user_id".to_string(),
            wallet_address: "******************************************".to_string(),
            node_id: "test_node_id".to_string(),
        }
    }

    #[test]
    // Loading a saved configuration file should return the same configuration.
    fn test_load_recovers_saved_config() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");

        let config = get_config();
        config.save(&path).unwrap();

        let loaded_config = Config::load_from_file(&path).unwrap();
        assert_eq!(config, loaded_config);
    }

    #[test]
    // Saving a configuration should create directories if they don't exist.
    fn test_save_creates_directories() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("nonexistent_dir").join("config.json");
        let config = get_config();
        let result = config.save(&path);

        // Check if the directories were created
        assert!(result.is_ok(), "Failed to save config");
        assert!(
            path.parent().unwrap().exists(),
            "Parent directory does not exist"
        );
    }

    #[test]
    // Saving a configuration should overwrite an existing file.
    fn test_save_overwrites_existing_file() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");

        // Create an initial config and save it
        let mut config1 = get_config();
        config1.user_id = "test_user_id".to_string();
        config1.save(&path).unwrap();

        // Create a new config and save it to the same path
        let mut config2 = get_config();
        config2.user_id = "new_test_user_id".to_string();
        config2.save(&path).unwrap();

        // Load the saved config and check if it matches the second one
        let loaded_config = Config::load_from_file(&path).unwrap();
        assert_eq!(config2, loaded_config);
    }

    #[test]
    // Loading an invalid JSON file should return an error.
    fn test_load_rejects_invalid_json() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("invalid_config.json");

        let mut file = File::create(&path).unwrap();
        writeln!(file, "invalid json").unwrap();

        let result = Config::load_from_file(&path);
        assert!(result.is_err());
    }

    #[test]
    // Clearing the node configuration file should remove it if it exists.
    fn test_clear_node_config_removes_file() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");
        let config = get_config();
        config.save(&path).unwrap();

        Config::clear_node_config(&path).unwrap();
        assert!(!path.exists(), "Config file was not removed");
    }

    #[test]
    // Should load JSON containing a user_id and empty strings for other fields.
    fn test_load_config_with_user_id_and_empty_fields() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");

        // Write a JSON with user_id and empty strings for other fields
        let mut file = File::create(&path).unwrap();
        writeln!(file, r#"{{ "user_id": "test_user", "wallet_address": "", "environment": "", "node_id": "" }}"#).unwrap();

        match Config::load_from_file(&path) {
            Ok(config) => {
                // The user_id must be set correctly.
                assert_eq!(config.user_id, "test_user");
                // Other fields should be empty or default
                assert!(config.wallet_address.is_empty());
                assert!(config.environment.is_empty());
                assert!(config.node_id.is_empty());
            }
            Err(e) => {
                panic!("Failed to load config with user_id and empty fields: {}", e);
            }
        }
    }

    #[test]
    // (Backwards compatibility) Should load JSON containing only node_id.
    fn test_load_config_with_only_node_id() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");

        // Write a minimal JSON with only node_id
        let mut file = File::create(&path).unwrap();
        writeln!(file, r#"{{ "node_id": "12345" }}"#).unwrap();

        match Config::load_from_file(&path) {
            Ok(config) => {
                // The node_id must be set correctly.
                assert_eq!(config.node_id, "12345");
                // Other fields should be empty or default
                assert!(config.user_id.is_empty());
                assert!(config.wallet_address.is_empty());
                assert!(config.environment.is_empty());
            }
            Err(e) => {
                panic!("Failed to load config with only node_id: {}", e);
            }
        }
    }

    #[test]
    // (Backwards compatibility) Should load JSON with node_id and empty strings for other fields.
    fn test_load_config_with_node_id_and_empty_strings() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");

        let config = Config {
            environment: "".to_string(),
            user_id: "".to_string(),
            wallet_address: "".to_string(),
            node_id: "12345".to_string(),
        };
        config.save(&path).unwrap();

        match Config::load_from_file(&path) {
            Ok(config) => {
                // The node_id must be set correctly.
                assert_eq!(config.node_id, "12345");
                // Other fields should be empty or default
                assert!(config.user_id.is_empty());
                assert!(config.wallet_address.is_empty());
                assert!(config.environment.is_empty());
            }
            Err(e) => {
                panic!("Failed to load config with only node_id: {}", e);
            }
        }
    }

    #[test]
    // Should ignore unexpected fields in the JSON.
    fn test_load_config_with_additional_fields() {
        let dir = tempdir().unwrap();
        let path = dir.path().join("config.json");

        // Write a JSON with additional fields
        let mut file = File::create(&path).unwrap();
        writeln!(file, r#"{{ "node_id": "12345", "extra_field": "value" }}"#).unwrap();

        match Config::load_from_file(&path) {
            Ok(config) => {
                // The node_id must be set correctly.
                assert_eq!(config.node_id, "12345");
                // Other fields should be empty or default
                assert!(config.user_id.is_empty());
                assert!(config.wallet_address.is_empty());
                assert!(config.environment.is_empty());
            }
            Err(e) => {
                panic!("Failed to load config with additional fields: {}", e);
            }
        }
    }
}
