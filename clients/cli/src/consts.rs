pub mod prover {
    //! Prover Configuration Constants
    //!
    //! This module contains all configuration constants for the prover system,
    //! organized by functional area for clarity and maintainability.

    // =============================================================================
    // QUEUE CONFIGURATION
    // =============================================================================
    // All queue sizes are chosen to be larger than the API page size (currently 50)
    // to provide adequate buffering while preventing excessive memory usage.

    /// Maximum number of tasks that can be queued for processing
    pub const TASK_QUEUE_SIZE: usize = 100;

    /// Maximum number of events that can be queued for UI updates
    pub const EVENT_QUEUE_SIZE: usize = 100;

    /// Maximum number of proof results that can be queued for submission
    pub const RESULT_QUEUE_SIZE: usize = 100;

    // =============================================================================
    // TASK FETCHING BEHAVIOR
    // =============================================================================

    /// Minimum queue level that triggers new task fetching
    /// When task queue drops below this threshold, fetch new tasks
    pub const LOW_WATER_MARK: usize = 1;

    /// Delay the fetch task time by this amount of seconds
    pub const FETCH_TASK_DELAY_TIME: u64 = 10;

    // =============================================================================
    // TIMING AND BACKOFF CONFIGURATION
    // =============================================================================

    /// Default backoff duration when retrying failed operations (milliseconds)
    /// Set to 2 minutes to balance responsiveness with server load
    pub const BACKOFF_DURATION: u64 = 120_000; // 2 minutes

    // =============================================================================
    // CACHE MANAGEMENT
    // =============================================================================

    /// Duration to keep task IDs in duplicate-prevention cache (milliseconds)
    /// Long enough to prevent immediate re-processing, short enough to allow
    /// eventual retry of legitimately failed tasks
    pub const CACHE_EXPIRATION: u64 = 300_000; // 5 minutes

    // =============================================================================
    // TASK EXPIRATION DETECTION
    // =============================================================================

    /// Number of consecutive 404 errors that trigger task queue cleanup
    /// When this many 404 errors occur in a row, we assume server tasks have expired
    pub const CONSECUTIVE_404_THRESHOLD: u32 = 3;

    /// Time window for counting consecutive 404 errors (milliseconds)
    /// 404 errors outside this window don't count toward the threshold
    pub const CONSECUTIVE_404_WINDOW: u64 = 60_000; // 1 minute

    /// Interval for logging queue status in debug mode (milliseconds)
    pub const QUEUE_LOG_INTERVAL: u64 = 30_000; // 30 seconds

    // =============================================================================
    // COMPUTED CONSTANTS
    // =============================================================================

    /// Maximum number of completed tasks to track (prevents memory growth)
    /// Reduced to 2x the task queue size for better memory efficiency
    /// This still provides adequate duplicate detection while reducing memory usage
    pub const MAX_COMPLETED_TASKS: usize = TASK_QUEUE_SIZE * 2;
}
