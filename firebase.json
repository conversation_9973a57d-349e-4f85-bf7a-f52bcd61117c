{"hosting": {"public": "public", "rewrites": [{"source": "/version.json", "destination": "/version.json"}, {"source": "**", "destination": "/install.sh"}], "headers": [{"source": "/version.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300"}]}, {"source": "/install.sh", "headers": [{"key": "Cache-Control", "value": "public, max-age=300"}, {"key": "Content-Type", "value": "text/x-shellscript"}]}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}]}