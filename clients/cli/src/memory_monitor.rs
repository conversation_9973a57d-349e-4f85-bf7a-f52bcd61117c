//! Memory monitoring and optimization for ZK proof computation
//!
//! This module provides real-time memory usage tracking to validate our
//! memory estimates and optimize resource allocation.

use std::sync::{Arc, OnceLock};
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tokio::time::interval;

/// Memory usage statistics
#[derive(Debug, <PERSON>lone)]
pub struct MemoryStats {
    pub current_usage_gb: f64,
    pub peak_usage_gb: f64,
    pub baseline_usage_gb: f64,
    pub proof_computation_usage_gb: f64,
    pub last_updated: Instant,
}

impl MemoryStats {
    pub fn new() -> Self {
        let current_usage = crate::system::process_memory_gb();
        Self {
            current_usage_gb: current_usage,
            peak_usage_gb: current_usage,
            baseline_usage_gb: current_usage,
            proof_computation_usage_gb: 0.0,
            last_updated: Instant::now(),
        }
    }

    pub fn update(&mut self) {
        let current_usage = crate::system::process_memory_gb();
        self.current_usage_gb = current_usage;
        
        if current_usage > self.peak_usage_gb {
            self.peak_usage_gb = current_usage;
        }
        
        // Calculate proof computation overhead
        self.proof_computation_usage_gb = current_usage - self.baseline_usage_gb;
        self.last_updated = Instant::now();
    }
}

/// Global memory monitor
pub struct MemoryMonitor {
    stats: Arc<Mutex<MemoryStats>>,
    monitoring_active: Arc<Mutex<bool>>,
}

impl MemoryMonitor {
    pub fn new() -> Self {
        Self {
            stats: Arc::new(Mutex::new(MemoryStats::new())),
            monitoring_active: Arc::new(Mutex::new(false)),
        }
    }

    /// Start memory monitoring with periodic updates
    pub async fn start_monitoring(&self) {
        let mut active = self.monitoring_active.lock().await;
        if *active {
            return; // Already monitoring
        }
        *active = true;
        drop(active);

        let stats = Arc::clone(&self.stats);
        let monitoring_active = Arc::clone(&self.monitoring_active);

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(60)); // Update every 60 seconds (减少频率)
            
            loop {
                interval.tick().await;
                
                // Check if monitoring should continue
                {
                    let active = monitoring_active.lock().await;
                    if !*active {
                        break;
                    }
                }
                
                // Update memory stats
                {
                    let mut stats = stats.lock().await;
                    stats.update();
                }
            }
        });
    }

    /// Stop memory monitoring
    pub async fn stop_monitoring(&self) {
        let mut active = self.monitoring_active.lock().await;
        *active = false;
    }

    /// Get current memory statistics
    pub async fn get_stats(&self) -> MemoryStats {
        let mut stats = self.stats.lock().await;
        stats.update(); // Ensure fresh data
        stats.clone()
    }

    /// Reset baseline (call this before starting proof computations)
    pub async fn reset_baseline(&self) {
        let mut stats = self.stats.lock().await;
        stats.baseline_usage_gb = crate::system::process_memory_gb();
        stats.peak_usage_gb = stats.baseline_usage_gb;
    }

    /// Log memory usage summary (only if significant change or debug mode)
    pub async fn log_summary(&self, context: &str) {
        let stats = self.get_stats().await;

        // Only log if memory usage is significant or in debug mode
        if stats.current_usage_gb > 1.0 || context.contains("Debug") || context.contains("Startup") {
            println!(
                "🧠 Memory [{}]: {:.2}GB (Peak: {:.2}GB)",
                context,
                stats.current_usage_gb,
                stats.peak_usage_gb
            );
        }
    }

    /// Force memory cleanup and garbage collection
    pub async fn force_cleanup(&self, context: &str) {
        // Log memory before cleanup
        let stats_before = self.get_stats().await;

        // Force garbage collection hints
        // Note: malloc_trim is available on Linux systems
        #[cfg(target_os = "linux")]
        {
            unsafe {
                libc::malloc_trim(0);
            }
        }

        // On some systems, we can try to force GC
        std::hint::black_box(());

        // Wait a moment for cleanup to take effect
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        // Log memory after cleanup
        let stats_after = self.get_stats().await;
        let freed_mb = (stats_before.current_usage_gb - stats_after.current_usage_gb) * 1024.0;

        // Only log significant memory cleanup (>50MB freed)
        if freed_mb > 50.0 {
            println!(
                "🧹 Cleanup [{}]: Freed {:.0}MB",
                context,
                freed_mb
            );
        }
    }

    /// Check if system is under memory pressure and needs aggressive cleanup
    pub async fn is_memory_pressure(&self) -> bool {
        let stats = self.get_stats().await;
        let system_memory_gb = crate::system::total_memory_gb();

        // Consider memory pressure if we're using more than 80% of system memory
        let usage_ratio = stats.current_usage_gb / system_memory_gb;
        usage_ratio > 0.8
    }

    /// Perform aggressive memory cleanup if under pressure
    pub async fn cleanup_if_pressure(&self, context: &str) {
        if self.is_memory_pressure().await {
            let stats = self.get_stats().await;
            println!("⚠️  Memory pressure: {:.1}GB, cleaning up...", stats.current_usage_gb);
            self.force_cleanup(&format!("Pressure-{}", context)).await;

            // Additional aggressive cleanup
            #[cfg(target_os = "linux")]
            {
                unsafe {
                    // More aggressive malloc cleanup
                    libc::malloc_trim(0);
                    std::thread::sleep(std::time::Duration::from_millis(50));
                    libc::malloc_trim(0);
                }
            }
        }
    }
}

// Global memory monitor instance using OnceLock for thread safety
static GLOBAL_MEMORY_MONITOR: OnceLock<MemoryMonitor> = OnceLock::new();

/// Initialize the global memory monitor
pub fn init_global_memory_monitor() {
    GLOBAL_MEMORY_MONITOR.get_or_init(|| MemoryMonitor::new());
}

/// Get the global memory monitor
pub fn get_global_memory_monitor() -> &'static MemoryMonitor {
    GLOBAL_MEMORY_MONITOR
        .get()
        .expect("Memory monitor not initialized. Call init_global_memory_monitor() first.")
}

/// Convenience function to start monitoring
pub async fn start_memory_monitoring() {
    get_global_memory_monitor().start_monitoring().await;
}

/// Convenience function to log memory usage
pub async fn log_memory_usage(context: &str) {
    get_global_memory_monitor().log_summary(context).await;
}

/// Convenience function to reset baseline before proof computation
pub async fn reset_memory_baseline() {
    get_global_memory_monitor().reset_baseline().await;
}

/// Convenience function to force memory cleanup
pub async fn force_cleanup(context: &str) {
    get_global_memory_monitor().force_cleanup(context).await;
}

/// Convenience function to cleanup if under memory pressure
pub async fn cleanup_if_pressure(context: &str) {
    get_global_memory_monitor().cleanup_if_pressure(context).await;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_memory_monitor() {
        let monitor = MemoryMonitor::new();
        
        // Start monitoring
        monitor.start_monitoring().await;
        
        // Wait a bit
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        // Get stats
        let stats = monitor.get_stats().await;
        assert!(stats.current_usage_gb > 0.0);
        
        // Stop monitoring
        monitor.stop_monitoring().await;
    }
}
